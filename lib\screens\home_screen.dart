import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/maintenance_provider.dart';
import '../models/maintenance_item.dart';
import '../models/document_item.dart';
import '../services/theme_service.dart';
import '../services/import_export_service.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_animations.dart';
import '../widgets/shimmer_loading.dart';
import '../widgets/admob_home_banner.dart';
import '../utils/admob_helper.dart';
import 'kilometer_input_screen.dart';
import 'vehicle_config_screen.dart';
import 'settings_screen.dart';
import 'notifications_screen.dart';
import 'maintenance_diagnostic_screen.dart';
import 'statistics_screen.dart';
import 'about_screen.dart';
import 'privacy_policy_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MaintenanceProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeService = context.watch<ThemeService>();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Carosti',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            letterSpacing: 1.0,
            color: Colors.white,
            fontSize: 16,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined, size: 22),
            onPressed: () => _navigateToNotifications(context),
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined, size: 22),
            onPressed: () => _navigateToSettings(context),
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
          // Menu principal moderne avec animation
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, size: 22, color: Colors.white),
            onSelected: (String value) => _handleMenuSelection(context, value),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 8,
            offset: const Offset(0, 8),
            splashRadius: 20,
            tooltip: 'Menu',
            itemBuilder: (BuildContext context) => [
              _buildMenuItemModern(
                'privacy',
                Icons.privacy_tip_outlined,
                'Politique de confidentialité',
                Colors.blue,
              ),
              const PopupMenuDivider(height: 1),
              _buildMenuItemModern(
                'import_export',
                Icons.import_export,
                'Import/Export',
                Colors.green,
              ),
              const PopupMenuDivider(height: 1),
              _buildMenuItemModern(
                'evaluation',
                Icons.star_outline,
                'Évaluation',
                Colors.orange,
              ),
              const PopupMenuDivider(height: 1),
              _buildMenuItemModern(
                'update',
                Icons.system_update,
                'Mise à jour',
                Colors.purple,
              ),
              const PopupMenuDivider(height: 1),
              _buildMenuItemModern(
                'about',
                Icons.info_outline,
                'À propos',
                Colors.teal,
              ),
              const PopupMenuDivider(height: 1),
              _buildMenuItemModern(
                'exit',
                Icons.exit_to_app,
                'Quitter',
                Colors.red,
              ),
            ],
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Container(
        decoration: themeService.createProfessionalBackground(),
        child: Consumer<MaintenanceProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading) {
              return Container(
                color: Colors.white,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Image animée de chargement CAROSTI - agrandie et sans ombres
                      Container(
                        width: 160, // Agrandi de 120 à 160
                        height: 160, // Agrandi de 120 à 160
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          // Suppression des boxShadow pour éliminer les ombres
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: Image.asset(
                            'assets/images/Movedicon.gif',
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              // Fallback si l'image ne charge pas
                              return Container(
                                decoration: BoxDecoration(
                                  color: themeService.primaryColor.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Icon(
                                  Icons.directions_car,
                                  size:
                                      80, // Agrandi de 60 à 80 pour correspondre
                                  color: themeService.primaryColor,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),
                      // Nom de l'application
                      Text(
                        'CAROSTI',
                        style: Theme.of(context).textTheme.headlineMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: themeService.primaryColor,
                              letterSpacing: 2,
                            ),
                      ),
                      const SizedBox(height: 16),
                      // Texte de chargement
                      Text(
                        'Chargement en cours...',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: ThemeService.professionalNeutrals['gray_600']!,
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Indicateur de progression moderne
                      Container(
                        width: 200,
                        height: 4,
                        decoration: BoxDecoration(
                          color: ThemeService.professionalNeutrals['gray_200']!,
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: LinearProgressIndicator(
                          backgroundColor: Colors.transparent,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            themeService.primaryColor,
                          ),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            if (provider.error != null) {
              return Center(
                child: Container(
                  margin: const EdgeInsets.all(24),
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red[400],
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'Oops !',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.w700),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        provider.error!,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: () {
                          provider.clearError();
                          provider.initialize();
                        },
                        child: const Text('Réessayer'),
                      ),
                    ],
                  ),
                ),
              );
            }

            return SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Widget véhicule actuel (remplace l'en-tête)
                    _buildVehicleInfoCard(context, provider),

                    const SizedBox(height: 32),

                    // Statistiques professionnelles
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: themeService.createProfessionalShadow(),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Vue d\'ensemble',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: ThemeService
                                      .professionalNeutrals['gray_900']!,
                                  fontSize: 14,
                                ),
                          ),
                          const SizedBox(height: 20),
                          Row(
                            children: [
                              Expanded(
                                child: _buildProfessionalStatCard(
                                  context,
                                  'Total',
                                  _getActiveMaintenanceItemsCount(provider).toString(),
                                  'entretiens',
                                  MdiIcons.wrench,
                                  ThemeService
                                      .professionalColors['corporate_blue']!,
                                  () => _navigateToTotalMaintenance(context),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildProfessionalStatCard(
                                  context,
                                  'Urgents',
                                  provider.itemsDue.length.toString(),
                                  'à traiter',
                                  MdiIcons.alertCircle,
                                  ThemeService.professionalSemantics['error']!,
                                  () => _navigateToUrgentMaintenance(
                                    context,
                                    provider,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: _buildProfessionalStatCard(
                                  context,
                                  'À venir',
                                  provider.itemsApproaching.length.toString(),
                                  'à planifier',
                                  MdiIcons.clockAlert,
                                  ThemeService
                                      .professionalSemantics['warning']!,
                                  () => _navigateToApproachingMaintenance(
                                    context,
                                    provider,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildProfessionalStatCard(
                                  context,
                                  'À jour',
                                  (_getActiveMaintenanceItemsCount(provider) -
                                          provider.itemsDue.length -
                                          provider.itemsApproaching.length)
                                      .toString(),
                                  'en ordre',
                                  MdiIcons.checkCircle,
                                  ThemeService
                                      .professionalSemantics['success']!,
                                  () => _navigateToOkMaintenance(
                                    context,
                                    provider,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Prochain entretien moderne
                    _buildModernNextMaintenanceSection(context, provider),

                    const SizedBox(height: 32),

                    // Section Documents (Assurance & Contrôle Technique)
                    _buildDocumentsSection(context, provider),

                    const SizedBox(height: 32),

                    // Actions rapides modernes
                    ModernFadeIn(
                      delay: const Duration(milliseconds: 300),
                      child: ModernCard(
                        padding: const EdgeInsets.all(24),
                        enableHoverEffect: true,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Actions rapides',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: ThemeService
                                      .professionalNeutrals['gray_900']!,
                                  fontSize: 14,
                                ),
                          ),
                          const SizedBox(height: 20),
                          Row(
                            children: [
                              Expanded(
                                child: _buildProfessionalActionButton(
                                  context,
                                  'Config véhicule',
                                  'Configurer',
                                  MdiIcons.carCog,
                                  () => _navigateToVehicleConfig(context),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildProfessionalActionButton(
                                  context,
                                  'Statistiques',
                                  'Analyser',
                                  MdiIcons.chartLine,
                                  () => _navigateToStatistics(context),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToKilometerInput(context),
        icon: Icon(MdiIcons.speedometer, size: 16),
        label: const Text(
          'Km',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 11),
        ),
        backgroundColor: themeService.primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        extendedPadding: const EdgeInsets.symmetric(horizontal: 12),
      ),
      bottomNavigationBar: const AdMobHomeBottomBanner(),
    );
  }

  // Méthode pour les cartes de statistiques professionnelles
  Widget _buildProfessionalStatCard(
    BuildContext context,
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(icon, color: color, size: 14),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: ThemeService.professionalNeutrals['gray_700']!,
                      fontSize: 11,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w700,
                color: color,
                fontSize: 20,
              ),
            ),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeService.professionalNeutrals['gray_500']!,
                fontWeight: FontWeight.w400,
                fontSize: 9,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Méthode pour les boutons d'action professionnels
  Widget _buildProfessionalActionButton(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: ThemeService.professionalNeutrals['gray_50']!,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: ThemeService.professionalNeutrals['gray_200']!,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ThemeService.professionalColors['corporate_blue']!
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: ThemeService.professionalColors['corporate_blue']!,
                size: 16,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w700,
                color: ThemeService.professionalNeutrals['gray_700']!,
                fontSize: 10,
              ),
            ),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeService.professionalNeutrals['gray_500']!,
                fontWeight: FontWeight.w600,
                fontSize: 9,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Nouvelle section moderne pour le prochain entretien
  Widget _buildModernNextMaintenanceSection(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    final stats = provider.getDetailedStatistics();
    final nextItem = stats['nextMaintenanceItem'] as MaintenanceItem?;
    final nextKm = stats['nextMaintenanceKm'] as int?;

    return ModernFadeIn(
      delay: const Duration(milliseconds: 100),
      child: ModernCard(
        padding: const EdgeInsets.all(16),
        enableHoverEffect: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                MdiIcons.clockAlert,
                color: ThemeService.professionalColors['corporate_blue']!,
                size: 18,
              ),
              const SizedBox(width: 12),
              Text(
                'Prochain',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeService.professionalNeutrals['gray_900']!,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          nextItem != null && nextKm != null
              ? _buildModernNextMaintenanceInfo(
                  context,
                  provider,
                  nextItem,
                  nextKm,
                )
              : _buildModernNoMaintenanceNeeded(context),
        ],
      ),
      ),
    );
  }

  Widget _buildModernNextMaintenanceInfo(
    BuildContext context,
    MaintenanceProvider provider,
    MaintenanceItem item,
    int remainingKm,
  ) {
    final wearPercentage = provider.calculateWearPercentage(item);

    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (wearPercentage >= 100) {
      statusColor = Colors.redAccent;
      statusIcon = MdiIcons.alertCircle;
      statusText = 'URGENT';
    } else if (wearPercentage >= 80) {
      statusColor = Colors.orangeAccent;
      statusIcon = MdiIcons.clockAlert;
      statusText = 'BIENTÔT';
    } else if (wearPercentage >= 60) {
      statusColor = Colors.blueAccent;
      statusIcon = MdiIcons.calendar;
      statusText = 'PLANIFIÉ';
    } else {
      statusColor = Colors.greenAccent;
      statusIcon = MdiIcons.checkCircle;
      statusText = 'OK';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              MdiIcons.wrench,
              color: ThemeService.professionalColors['corporate_blue']!,
              size: 16,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                item.name,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeService.professionalNeutrals['gray_900']!,
                  fontSize: 12,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: statusColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(statusIcon, color: statusColor, size: 14),
                  const SizedBox(width: 6),
                  Text(
                    statusText,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 8,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              child: _buildModernInfoItem(
                context,
                'Kilomètres restants',
                '$remainingKm km',
                MdiIcons.speedometer,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildModernInfoItem(
                context,
                'Usure',
                '${wearPercentage.toInt()}%',
                MdiIcons.gauge,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModernNoMaintenanceNeeded(BuildContext context) {
    return Column(
      children: [
        Icon(
          MdiIcons.checkCircleOutline,
          size: 48,
          color: ThemeService.professionalSemantics['success']!,
        ),
        const SizedBox(height: 16),
        Text(
          'Tout va bien !',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: ThemeService.professionalNeutrals['gray_900']!,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Aucun entretien imminent',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: ThemeService.professionalNeutrals['gray_600']!,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildModernInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeService.professionalNeutrals['gray_50']!,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ThemeService.professionalNeutrals['gray_200']!,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: ThemeService.professionalColors['corporate_blue']!,
            size: 18,
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: ThemeService.professionalNeutrals['gray_900']!,
              fontSize: 14,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeService.professionalNeutrals['gray_600']!,
              fontSize: 9,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Section Documents (Assurance & Contrôle Technique)
  Widget _buildDocumentsSection(BuildContext context, MaintenanceProvider provider) {
    final documents = provider.documentItems;

    print('🔍 Documents trouvés: ${documents.length}');
    for (final doc in documents) {
      print('📄 Document: ${doc.name} - Expire: ${doc.expirationDate}');
    }

    if (documents.isEmpty) {
      print('❌ Aucun document trouvé - section masquée');
      return const SizedBox.shrink();
    }

    return ModernFadeIn(
      delay: const Duration(milliseconds: 200),
      child: ModernCard(
        padding: const EdgeInsets.all(24),
        enableHoverEffect: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ThemeService.professionalColors['corporate_blue']!.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  MdiIcons.fileDocument,
                  color: ThemeService.professionalColors['corporate_blue']!,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Documents',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeService.professionalNeutrals['gray_900']!,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...documents.map((document) => _buildDocumentCard(context, document)),
        ],
      ),
      ),
    );
  }

  Widget _buildDocumentCard(BuildContext context, DocumentItem document) {
    final daysRemaining = document.daysUntilExpiration;
    final isExpired = document.isExpired;
    final isUrgent = document.isUrgent;
    final isExpiringSoon = document.isExpiringSoon;

    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (isExpired) {
      statusColor = Colors.red;
      statusText = 'EXPIRÉ';
      statusIcon = MdiIcons.alertCircle;
    } else if (isUrgent) {
      statusColor = Colors.red;
      statusText = 'URGENT';
      statusIcon = MdiIcons.alertCircle;
    } else if (daysRemaining <= 15) {
      statusColor = Colors.orange;
      statusText = 'BIENTÔT';
      statusIcon = MdiIcons.clockAlert;
    } else if (isExpiringSoon) {
      statusColor = Colors.blue;
      statusText = 'PLANIFIÉ';
      statusIcon = MdiIcons.calendar;
    } else {
      statusColor = Colors.green;
      statusText = 'VALIDE';
      statusIcon = MdiIcons.checkCircle;
    }

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const MaintenanceDiagnosticScreen(
              scrollToDocuments: true,
            ),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ThemeService.professionalNeutrals['gray_50']!,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: ThemeService.professionalNeutrals['gray_200']!,
          ),
        ),
        child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              statusIcon,
              color: statusColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ThemeService.professionalNeutrals['gray_900']!,
                    fontSize: 11,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  document.expirationDate != null
                      ? 'Reste: ${daysRemaining > 0 ? daysRemaining : 0} jours'
                      : 'Date non définie',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: ThemeService.professionalNeutrals['gray_600']!,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              statusText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 8,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    ),
    );
  }

  // Méthode pour compter seulement les entretiens de base actifs (exclure documents et "autre")
  int _getActiveMaintenanceItemsCount(MaintenanceProvider provider) {
    return provider.maintenanceItems
        .where((item) =>
            item.isActive &&
            item.category.toLowerCase() != 'autre' &&
            !item.name.startsWith('[CUSTOM]')) // Exclure les entretiens personnalisés temporaires
        .length;
  }

  // Méthodes de navigation
  void _navigateToKilometerInput(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const KilometerInputScreen()),
    );
  }

  void _navigateToVehicleConfig(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const VehicleConfigScreen()),
    );
  }

  void _navigateToSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  void _navigateToNotifications(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NotificationsScreen()),
    );
  }

  void _navigateToTotalMaintenance(BuildContext context) {
    // Naviguer vers le diagnostic qui montre tous les entretiens avec leurs statuts
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MaintenanceDiagnosticScreen(),
      ),
    );
  }

  void _navigateToStatistics(BuildContext context) {
    // Navigation avec publicité interstitielle
    AdMobHelper.showInterstitialAndNavigate(
      context,
      const StatisticsScreen(),
    );
  }

  // Navigation vers les entretiens filtrés par statut
  void _navigateToUrgentMaintenance(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    // Naviguer vers le diagnostic avec filtre "Urgent"
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            const MaintenanceDiagnosticScreen(initialFilter: 'Urgent'),
      ),
    );
  }

  void _navigateToApproachingMaintenance(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    // Naviguer vers le diagnostic avec filtre "À planifier"
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            const MaintenanceDiagnosticScreen(initialFilter: 'À planifier'),
      ),
    );
  }

  void _navigateToOkMaintenance(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    // Naviguer vers le diagnostic avec filtre "OK"
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            const MaintenanceDiagnosticScreen(initialFilter: 'OK'),
      ),
    );
  }

  // Widget tableau de bord moderne
  Widget _buildVehicleInfoCard(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    final themeService = context.read<ThemeService>();

    if (provider.currentVehicle == null) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              themeService.primaryColor.withValues(alpha: 0.1),
              themeService.primaryColor.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: themeService.primaryColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: themeService.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: themeService.primaryColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    MdiIcons.viewDashboard,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Tableau de bord',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: ThemeService.professionalNeutrals['gray_900']!,
                      fontSize: 18,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.orange.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(MdiIcons.alertCircle, color: Colors.orange, size: 18),
                  const SizedBox(width: 12),
                  Text(
                    'Aucun véhicule configuré',
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    final vehicle = provider.currentVehicle!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            themeService.primaryColor.withValues(alpha: 0.1),
            themeService.primaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: themeService.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: themeService.primaryColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: themeService.primaryColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: themeService.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  MdiIcons.viewDashboard,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'Tableau de bord',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: ThemeService.professionalNeutrals['gray_900']!,
                    fontSize: 18,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          Row(
            children: [
              Expanded(
                child: _buildSimpleInfoRow(
                  'Véhicule',
                  vehicle.vehicleName,
                  MdiIcons.car,
                  themeService.primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSimpleInfoRow(
                  'Kilométrage',
                  '${vehicle.currentKilometers} km',
                  MdiIcons.speedometer,
                  Colors.blue,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildSimpleInfoRow(
                  'Marque',
                  vehicle.brand,
                  MdiIcons.carInfo,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSimpleInfoRow(
                  'Dernière MAJ',
                  _formatDate(vehicle.lastKmUpdate),
                  MdiIcons.clockOutline,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleInfoRow(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: ThemeService.professionalNeutrals['gray_600']!,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Padding(
          padding: const EdgeInsets.only(left: 24),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: ThemeService.professionalNeutrals['gray_900']!,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Afficher un message de succès moderne
  void _showModernSnackBar(
    BuildContext context,
    String message,
    Color color,
    IconData icon, {
    int durationSeconds = 4,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(icon, color: Colors.white, size: 16),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
        duration: Duration(seconds: durationSeconds),
      ),
    );
  }

  // Construire un élément de menu moderne
  PopupMenuItem<String> _buildMenuItemModern(
    String value,
    IconData icon,
    String title,
    Color iconColor,
  ) {
    return PopupMenuItem<String>(
      value: value,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, size: 18, color: iconColor),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Gestion des sélections du menu
  void _handleMenuSelection(BuildContext context, String value) {
    // Exécution directe sans délai pour éviter les problèmes de BuildContext
    switch (value) {
      case 'privacy':
        _showPrivacyPolicy(context);
        break;
      case 'import_export':
        _showImportExport(context);
        break;
      case 'evaluation':
        _showEvaluation(context);
        break;
      case 'update':
        _showUpdate(context);
        break;
      case 'about':
        _showAbout(context);
        break;
      case 'exit':
        _showExitConfirmation(context);
        break;
    }
  }

  // Politique de confidentialité
  void _showPrivacyPolicy(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
    );
  }

  // Import/Export
  void _showImportExport(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.import_export,
                  color: Colors.green,
                  size: 18,
                ),
              ),
              const SizedBox(width: 10),
              const Text(
                'Import/Export',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.save_alt, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Export: Créer sauvegarde (.dat)',
                      style: TextStyle(fontSize: 13),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.folder_open, color: Colors.blue, size: 16),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Import: Restaurer sauvegarde',
                      style: TextStyle(fontSize: 13),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: const Text(
                      'Annuler',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _handleImport(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.folder_open, size: 14),
                        const SizedBox(width: 4),
                        const Text('Import', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                // Bouton de test temporaire
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      Navigator.of(context).pop(); // Fermer le dialog
                      await ImportExportService.testExportImport();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.bug_report, size: 14),
                        const SizedBox(width: 4),
                        const Text('Test', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _handleExport(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.save_alt, size: 14),
                        const SizedBox(width: 4),
                        const Text('Export', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  // Gérer l'export
  Future<void> _handleExport(BuildContext context) async {
    Navigator.of(context).pop(); // Fermer le dialog

    // Afficher un indicateur de chargement
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Export en cours...'),
          ],
        ),
      ),
    );

    final success = await ImportExportService.exportData(context);

    if (context.mounted) {
      Navigator.of(context).pop(); // Fermer l'indicateur de chargement

      _showModernSnackBar(
        context,
        success ? 'Export réussi!' : 'Erreur lors de l\'export',
        success ? Colors.green : Colors.red,
        success ? Icons.check_circle : Icons.error,
      );
    }
  }

  // Gérer l'import
  Future<void> _handleImport(BuildContext context) async {
    Navigator.of(context).pop(); // Fermer le dialog

    // Lancer l'import avec recréation de base
    debugPrint('🏠 HOME: Starting import...');
    try {
      final result = await ImportExportService.importData();
      debugPrint('🏠 HOME: Import result: ${result.success}');
      debugPrint('🏠 HOME: Import needsReload: ${result.needsReload}');
      debugPrint('🏠 HOME: Context mounted: ${context.mounted}');

      if (context.mounted) {
        debugPrint('🏠 HOME: Contexte monté, vérification du succès...');
        if (result.success) {
          // Recharger TOUTES les données du provider pour afficher les données importées
          try {
            final provider = context.read<MaintenanceProvider>();
            debugPrint('🔄 Rechargement complet après import...');
            await provider.reloadAllData(); // ⭐ UTILISER LA NOUVELLE MÉTHODE
            debugPrint('✅ Toutes les données rechargées après import');
          } catch (e) {
            debugPrint('❌ Erreur lors du rechargement: $e');
          }

          if (context.mounted) {
            // Afficher le message de succès
            _showModernSnackBar(
              context,
              result.message,
              Colors.green,
              Icons.check_circle,
            );
          }
        } else {
          // Afficher seulement les erreurs
          _showModernSnackBar(
            context,
            'Erreur d\'import: ${result.message}',
            Colors.red,
            Icons.error,
          );
        }
      }
    } catch (error) {
      if (context.mounted) {
        _showModernSnackBar(
          context,
          'Erreur: $error',
          Colors.red,
          Icons.error,
        );
      }
    }
  }

  // Évaluation
  void _showEvaluation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.star_outline,
                  color: Colors.orange,
                  size: 18,
                ),
              ),
              const SizedBox(width: 10),
              const Text(
                'Évaluer Carosti',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.star_rate, color: Colors.orange, size: 16),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Vous aimez Carosti ?',
                      style: TextStyle(fontSize: 13),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.store, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Évaluez-nous sur Google Play',
                      style: TextStyle(fontSize: 13),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: const Text(
                      'Plus tard',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      Navigator.of(context).pop();
                      try {
                        // Ouvrir directement la page Google Play de l'app
                        final Uri playStoreUri = Uri.parse(
                          'https://play.google.com/store/apps/details?id=com.carosti.app',
                        );

                        if (await canLaunchUrl(playStoreUri)) {
                          await launchUrl(
                            playStoreUri,
                            mode: LaunchMode.externalApplication,
                          );
                        } else {
                          if (context.mounted) {
                            _showModernSnackBar(
                              context,
                              'Impossible d\'ouvrir le Play Store',
                              Colors.red,
                              Icons.error,
                            );
                          }
                        }
                      } catch (e) {
                        if (context.mounted) {
                          _showModernSnackBar(
                            context,
                            'Erreur lors de l\'ouverture',
                            Colors.red,
                            Icons.error,
                          );
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.star, size: 14),
                        const SizedBox(width: 4),
                        const Text('Évaluer', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  // Mise à jour
  void _showUpdate(BuildContext context) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.system_update,
                  color: Colors.purple,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text('Mise à jour'),
            ],
          ),
          content: const Text(
            '🔄 Vérifier les mises à jour sur le Play Store\n\n'
            '✨ Nouvelles fonctionnalités et améliorations.\n\n'
            '📱 Voulez-vous ouvrir le Play Store ?',
            style: TextStyle(height: 1.5),
          ),
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                    child: const Text('Annuler'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      Navigator.of(context).pop();
                      try {
                        // Ouvrir directement la page Google Play de l'app
                        final Uri playStoreUri = Uri.parse(
                          'https://play.google.com/store/apps/details?id=com.carosti.app',
                        );

                        if (await canLaunchUrl(playStoreUri)) {
                          await launchUrl(
                            playStoreUri,
                            mode: LaunchMode.externalApplication,
                          );
                        } else {
                          if (context.mounted) {
                            _showModernSnackBar(
                              context,
                              'Impossible d\'ouvrir le Play Store',
                              Colors.red,
                              Icons.error,
                            );
                          }
                        }
                      } catch (e) {
                        if (context.mounted) {
                          _showModernSnackBar(
                            context,
                            'Erreur lors de l\'ouverture',
                            Colors.red,
                            Icons.error,
                          );
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('🔍 Vérifier'),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  // Reload base de données (même fonctionnalité que dans le diagnostic)
  Future<void> _reloadDatabase(BuildContext context) async {
    try {
      final provider = context.read<MaintenanceProvider>();

      // Afficher un indicateur de chargement moderne
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Recréation de la base...'),
            ],
          ),
        ),
      );

      // Recréer la base de données (même méthode que le diagnostic)
      await provider.recreateDatabase();

      // Fermer l'indicateur de chargement
      if (context.mounted) {
        Navigator.of(context).pop();

        // Afficher un message de succès moderne
        _showModernSnackBar(
          context,
          '✅ Base de données recréée',
          Colors.green,
          Icons.check_circle,
        );
      }
    } catch (e) {
      // Fermer l'indicateur de chargement en cas d'erreur
      if (context.mounted) {
        Navigator.of(context).pop();

        // Afficher un message d'erreur moderne
        _showModernSnackBar(
          context,
          '❌ Erreur: $e',
          Colors.red,
          Icons.error,
        );
      }
    }
  }

  // À propos
  void _showAbout(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AboutScreen()));
  }

  // Confirmation de sortie
  void _showExitConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.exit_to_app,
                  color: Colors.red,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text('Quitter Carosti'),
            ],
          ),
          content: const Text(
            '👋 Êtes-vous sûr de vouloir quitter l\'application ?\n\n'
            '💾 Toutes vos données sont automatiquement sauvegardées.',
            style: TextStyle(height: 1.5),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                SystemNavigator.pop(); // Ferme l'application
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('🚪 Quitter'),
            ),
          ],
        );
      },
    );
  }
}
