import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';
import '../models/maintenance_item.dart';
import '../models/vehicle_config.dart';
import '../models/maintenance_history.dart';
import '../models/default_maintenance_items.dart';
import '../models/document_item.dart';
import '../models/default_document_items.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'carosti.db');
    return await openDatabase(path, version: 3, onCreate: _onCreate, onUpgrade: _onUpgrade);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('🔄 Mise à jour de la base de données de v$oldVersion vers v$newVersion');

    if (oldVersion < 2) {
      // Vérifier si la table existe déjà
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='document_items'"
      );

      if (result.isEmpty) {
        // Ajouter la table des documents seulement si elle n'existe pas
        await db.execute('''
          CREATE TABLE document_items(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT NOT NULL,
            iconPath TEXT NOT NULL,
            expirationDate INTEGER,
            companyName TEXT,
            documentNumber TEXT,
            notes TEXT,
            category TEXT NOT NULL,
            isActive INTEGER NOT NULL DEFAULT 1
          )
        ''');

        print('📄 Table document_items créée');

        // Insérer les documents par défaut
        await _insertDefaultDocumentItems(db);
        print('📋 Documents par défaut insérés');
      } else {
        print('📄 Table document_items existe déjà');

        // Vérifier si les documents par défaut existent
        final docs = await db.query('document_items');
        if (docs.isEmpty) {
          await _insertDefaultDocumentItems(db);
          print('📋 Documents par défaut insérés');
        } else {
          print('📋 Documents par défaut existent déjà');
        }
      }
    }

    if (oldVersion < 3) {
      print('🔄 Migration vers v3 - Ajout de vehicle_id aux tables');

      // Ajouter vehicle_id à maintenance_items
      await db.execute('ALTER TABLE maintenance_items ADD COLUMN vehicle_id INTEGER');

      // Ajouter vehicle_id à document_items
      await db.execute('ALTER TABLE document_items ADD COLUMN vehicle_id INTEGER');

      // Obtenir le véhicule par défaut
      final vehicles = await db.query('vehicle_configs', where: 'isDefault = ?', whereArgs: [1]);
      if (vehicles.isNotEmpty) {
        final defaultVehicleId = vehicles.first['id'];

        // Associer tous les entretiens existants au véhicule par défaut
        await db.update('maintenance_items', {'vehicle_id': defaultVehicleId});

        // Associer tous les documents existants au véhicule par défaut
        await db.update('document_items', {'vehicle_id': defaultVehicleId});

        print('📋 Données existantes associées au véhicule par défaut (ID: $defaultVehicleId)');
      }

      print('✅ Migration v3 terminée');
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    // Table des configurations de véhicule
    await db.execute('''
      CREATE TABLE vehicle_configs(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        vehicleName TEXT NOT NULL,
        brand TEXT NOT NULL,
        model TEXT NOT NULL,
        year INTEGER NOT NULL,
        currentKilometers INTEGER NOT NULL,
        lastKmUpdate INTEGER NOT NULL,
        licensePlate TEXT,
        engineType TEXT,
        isDefault INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Table des éléments d'entretien
    await db.execute('''
      CREATE TABLE maintenance_items(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        iconPath TEXT NOT NULL,
        defaultIntervalKm INTEGER NOT NULL,
        customIntervalKm INTEGER,
        currentKm INTEGER NOT NULL,
        lastMaintenanceKm INTEGER NOT NULL,
        lastMaintenanceDate INTEGER,
        isActive INTEGER NOT NULL DEFAULT 1,
        category TEXT NOT NULL,
        vehicle_id INTEGER
      )
    ''');

    // Table de l'historique d'entretien
    await db.execute('''
      CREATE TABLE maintenance_history(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        maintenanceItemId INTEGER NOT NULL,
        kilometersAtMaintenance INTEGER NOT NULL,
        maintenanceDate INTEGER NOT NULL,
        notes TEXT,
        cost REAL,
        location TEXT,
        mechanicName TEXT,
        photos TEXT,
        isCompleted INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (maintenanceItemId) REFERENCES maintenance_items (id)
      )
    ''');

    // Table des documents (Assurance, Contrôle Technique)
    await db.execute('''
      CREATE TABLE document_items(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        iconPath TEXT NOT NULL,
        expirationDate INTEGER,
        companyName TEXT,
        documentNumber TEXT,
        notes TEXT,
        category TEXT NOT NULL,
        isActive INTEGER NOT NULL DEFAULT 1,
        vehicle_id INTEGER
      )
    ''');

    // Insérer les éléments d'entretien par défaut
    await _insertDefaultMaintenanceItems(db);

    // Insérer les documents par défaut
    await _insertDefaultDocumentItems(db);
  }

  Future<void> _insertDefaultMaintenanceItems(Database db) async {
    final defaultItems = DefaultMaintenanceItems.getDefaultItems();
    for (final item in defaultItems) {
      await db.insert('maintenance_items', item.toMap());
    }
  }

  Future<void> _insertDefaultDocumentItems(Database db) async {
    final defaultItems = DefaultDocumentItems.getDefaultItems();
    for (final item in defaultItems) {
      await db.insert('document_items', item.toMap());
    }
  }

  // CRUD pour VehicleConfig
  Future<int> insertVehicleConfig(VehicleConfig config) async {
    final db = await database;
    return await db.insert('vehicle_configs', config.toMap());
  }

  Future<List<VehicleConfig>> getAllVehicleConfigs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('vehicle_configs');
    return List.generate(maps.length, (i) => VehicleConfig.fromMap(maps[i]));
  }

  Future<VehicleConfig?> getDefaultVehicleConfig() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'vehicle_configs',
      where: 'isDefault = ?',
      whereArgs: [1],
      limit: 1,
    );
    return maps.isNotEmpty ? VehicleConfig.fromMap(maps.first) : null;
  }

  Future<int> updateVehicleConfig(VehicleConfig config) async {
    final db = await database;
    return await db.update(
      'vehicle_configs',
      config.toMap(),
      where: 'id = ?',
      whereArgs: [config.id],
    );
  }

  Future<int> deleteVehicleConfig(int id) async {
    final db = await database;
    return await db.delete('vehicle_configs', where: 'id = ?', whereArgs: [id]);
  }

  /// Supprime un véhicule et TOUTES ses données associées
  Future<void> deleteVehicleAndAllData(int vehicleId) async {
    final db = await database;

    debugPrint('🗑️ Suppression complète véhicule ID: $vehicleId');

    // 1. Supprimer l'historique lié aux entretiens de ce véhicule
    final historyDeleted = await db.rawDelete('''
      DELETE FROM maintenance_history
      WHERE maintenanceItemId IN (
        SELECT id FROM maintenance_items WHERE vehicle_id = ?
      )
    ''', [vehicleId]);
    debugPrint('✅ Historique supprimé: $historyDeleted entrées');

    // 2. Supprimer les entretiens de ce véhicule
    final itemsDeleted = await db.delete(
      'maintenance_items',
      where: 'vehicle_id = ?',
      whereArgs: [vehicleId]
    );
    debugPrint('✅ Entretiens supprimés: $itemsDeleted entrées');

    // 3. Supprimer les documents de ce véhicule
    final docsDeleted = await db.delete(
      'document_items',
      where: 'vehicle_id = ?',
      whereArgs: [vehicleId]
    );
    debugPrint('✅ Documents supprimés: $docsDeleted entrées');

    // 4. Supprimer le véhicule lui-même
    final vehicleDeleted = await db.delete(
      'vehicle_configs',
      where: 'id = ?',
      whereArgs: [vehicleId]
    );
    debugPrint('✅ Véhicule supprimé: $vehicleDeleted entrée');

    debugPrint('🎯 Suppression complète terminée pour véhicule $vehicleId');
  }

  // CRUD pour MaintenanceItem
  Future<int> insertMaintenanceItem(MaintenanceItem item) async {
    final db = await database;
    return await db.insert('maintenance_items', item.toMap());
  }

  Future<List<MaintenanceItem>> getAllMaintenanceItems() async {
    final db = await database;
    // Récupérer seulement les éléments d'entretien ACTIFS pour les calculs
    final List<Map<String, dynamic>> maps = await db.query(
      'maintenance_items',
      where: 'isActive = ?',
      whereArgs: [1],
    );
    final items = List.generate(
      maps.length,
      (i) => MaintenanceItem.fromMap(maps[i]),
    );

    // Trier selon l'ordre défini dans DefaultMaintenanceItems
    final defaultItems = DefaultMaintenanceItems.getDefaultItems();
    final orderMap = <String, int>{};
    for (int i = 0; i < defaultItems.length; i++) {
      orderMap[defaultItems[i].name] = i;
    }

    items.sort((a, b) {
      final orderA = orderMap[a.name] ?? 999;
      final orderB = orderMap[b.name] ?? 999;
      return orderA.compareTo(orderB);
    });

    return items;
  }

  Future<List<MaintenanceItem>> getMaintenanceItemsByVehicle(int vehicleId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'maintenance_items',
      where: 'isActive = ? AND vehicle_id = ?',
      whereArgs: [1, vehicleId],
      orderBy: 'name',
    );
    return List.generate(maps.length, (i) => MaintenanceItem.fromMap(maps[i]));
  }

  // Méthode spéciale pour récupérer TOUS les éléments (actifs ET inactifs) pour la page intervalles
  Future<List<MaintenanceItem>> getAllMaintenanceItemsForSettings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('maintenance_items');
    final items = List.generate(
      maps.length,
      (i) => MaintenanceItem.fromMap(maps[i]),
    );

    // Trier selon l'ordre défini dans DefaultMaintenanceItems
    final defaultItems = DefaultMaintenanceItems.getDefaultItems();
    final orderMap = <String, int>{};
    for (int i = 0; i < defaultItems.length; i++) {
      orderMap[defaultItems[i].name] = i;
    }

    items.sort((a, b) {
      final orderA = orderMap[a.name] ?? 999;
      final orderB = orderMap[b.name] ?? 999;
      return orderA.compareTo(orderB);
    });

    return items;
  }

  // Méthode pour récupérer les éléments d'un véhicule spécifique pour les paramètres
  Future<List<MaintenanceItem>> getMaintenanceItemsForSettingsByVehicle(int vehicleId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'maintenance_items',
      where: 'vehicle_id = ?',
      whereArgs: [vehicleId],
    );
    final items = List.generate(
      maps.length,
      (i) => MaintenanceItem.fromMap(maps[i]),
    );

    // Trier selon l'ordre défini dans DefaultMaintenanceItems
    final defaultItems = DefaultMaintenanceItems.getDefaultItems();
    final orderMap = <String, int>{};
    for (int i = 0; i < defaultItems.length; i++) {
      orderMap[defaultItems[i].name] = i;
    }

    items.sort((a, b) {
      final orderA = orderMap[a.name] ?? 999;
      final orderB = orderMap[b.name] ?? 999;
      return orderA.compareTo(orderB);
    });

    return items;
  }

  Future<List<MaintenanceItem>> getMaintenanceItemsByCategory(
    String category,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'maintenance_items',
      where: 'category = ? AND isActive = ?',
      whereArgs: [category, 1],
      orderBy: 'name',
    );
    return List.generate(maps.length, (i) => MaintenanceItem.fromMap(maps[i]));
  }

  Future<int> updateMaintenanceItem(MaintenanceItem item) async {
    final db = await database;
    return await db.update(
      'maintenance_items',
      item.toMap(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<int> updateCurrentKilometers(int kilometers) async {
    final db = await database;
    return await db.update('maintenance_items', {'currentKm': kilometers});
  }

  Future<int> updateCurrentKilometersForVehicle(int kilometers, int vehicleId) async {
    final db = await database;
    return await db.update(
      'maintenance_items',
      {'currentKm': kilometers},
      where: 'vehicle_id = ?',
      whereArgs: [vehicleId],
    );
  }

  // CRUD pour MaintenanceHistory
  Future<int> insertMaintenanceHistory(MaintenanceHistory history) async {
    final db = await database;
    return await db.insert('maintenance_history', history.toMap());
  }

  Future<List<MaintenanceHistory>> getMaintenanceHistoryForItem(
    int itemId,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'maintenance_history',
      where: 'maintenanceItemId = ?',
      whereArgs: [itemId],
      orderBy: 'maintenanceDate DESC',
    );
    return List.generate(
      maps.length,
      (i) => MaintenanceHistory.fromMap(maps[i]),
    );
  }

  Future<List<MaintenanceHistory>> getAllMaintenanceHistory() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'maintenance_history',
      orderBy: 'maintenanceDate DESC',
    );
    return List.generate(
      maps.length,
      (i) => MaintenanceHistory.fromMap(maps[i]),
    );
  }

  /// Récupère l'historique d'entretien pour un véhicule spécifique
  Future<List<MaintenanceHistory>> getMaintenanceHistoryByVehicle(int vehicleId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT mh.* FROM maintenance_history mh
      INNER JOIN maintenance_items mi ON mh.maintenanceItemId = mi.id
      WHERE mi.vehicle_id = ?
      ORDER BY mh.maintenanceDate DESC
    ''', [vehicleId]);

    return List.generate(
      maps.length,
      (i) => MaintenanceHistory.fromMap(maps[i]),
    );
  }

  Future<int> deleteMaintenanceHistory(int historyId) async {
    final db = await database;
    return await db.delete(
      'maintenance_history',
      where: 'id = ?',
      whereArgs: [historyId],
    );
  }

  /// Met à jour le kilométrage d'un entretien dans l'historique
  Future<int> updateMaintenanceHistoryKilometers(
    int historyId,
    int newKilometers,
  ) async {
    final db = await database;
    return await db.update(
      'maintenance_history',
      {'kilometersAtMaintenance': newKilometers},
      where: 'id = ?',
      whereArgs: [historyId],
    );
  }

  // CRUD pour DocumentItem
  Future<int> insertDocumentItem(DocumentItem item) async {
    final db = await database;
    return await db.insert('document_items', item.toMap());
  }

  Future<List<DocumentItem>> getAllDocumentItems() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('document_items');
    return List.generate(maps.length, (i) => DocumentItem.fromMap(maps[i]));
  }

  Future<List<DocumentItem>> getDocumentItemsByVehicle(int vehicleId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'document_items',
      where: 'vehicle_id = ?',
      whereArgs: [vehicleId],
    );
    return List.generate(maps.length, (i) => DocumentItem.fromMap(maps[i]));
  }

  Future<DocumentItem?> getDocumentItemById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'document_items',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    return maps.isNotEmpty ? DocumentItem.fromMap(maps.first) : null;
  }

  Future<int> updateDocumentItem(DocumentItem item) async {
    final db = await database;
    return await db.update(
      'document_items',
      item.toMap(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<int> deleteDocumentItem(int id) async {
    final db = await database;
    return await db.delete(
      'document_items',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Méthodes utilitaires
  Future<void> resetDatabase() async {
    final db = await database;
    await db.delete('maintenance_history');
    await db.delete('maintenance_items');
    await db.delete('document_items');
    await db.delete('vehicle_configs');
    await _insertDefaultMaintenanceItems(db);
    await _insertDefaultDocumentItems(db);
  }

  /// Force la recréation complète de la base de données
  Future<void> recreateDatabase() async {
    final dbPath = join(await getDatabasesPath(), 'maintenance.db');
    await deleteDatabase(dbPath);
    _database = null; // Forcer la recréation
    await database; // Recréer la base
  }

  // Créer les données par défaut pour un nouveau véhicule
  Future<void> createDefaultDataForVehicle(int vehicleId) async {
    final db = await database;

    // Créer les entretiens par défaut pour ce véhicule
    final defaultItems = DefaultMaintenanceItems.getDefaultItems();
    for (final item in defaultItems) {
      final itemWithVehicle = item.copyWith(
        id: null, // Nouveau ID
        currentKm: 0,
        lastMaintenanceKm: 0,
      );
      await db.insert('maintenance_items', {
        ...itemWithVehicle.toMap(),
        'vehicle_id': vehicleId,
      });
    }

    // Créer les documents par défaut pour ce véhicule
    final defaultDocuments = DefaultDocumentItems.getDefaultItems();
    for (final doc in defaultDocuments) {
      final docWithVehicle = doc.copyWith(
        id: null, // Nouveau ID
      );
      await db.insert('document_items', {
        ...docWithVehicle.toMap(),
        'vehicle_id': vehicleId,
      });
    }

    print('📋 Données par défaut créées pour le véhicule ID: $vehicleId');
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
