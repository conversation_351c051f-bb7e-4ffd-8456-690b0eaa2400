class VehicleConfig {
  final int? id;
  final String vehicleName;
  final String brand;
  final String model;
  final int year;
  final int currentKilometers;
  final DateTime lastKmUpdate;
  final String? licensePlate;
  final String? engineType;
  final bool isDefault;

  VehicleConfig({
    this.id,
    required this.vehicleName,
    required this.brand,
    required this.model,
    required this.year,
    required this.currentKilometers,
    required this.lastKmUpdate,
    this.licensePlate,
    this.engineType,
    this.isDefault = false,
  });

  // Convertir vers Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'vehicleName': vehicleName,
      'brand': brand,
      'model': model,
      'year': year,
      'currentKilometers': currentKilometers,
      'lastKmUpdate': lastKmUpdate.millisecondsSinceEpoch,
      'licensePlate': licensePlate,
      'engineType': engineType,
      'isDefault': isDefault ? 1 : 0,
    };
  }

  // Créer depuis Map (base de données)
  factory VehicleConfig.fromMap(Map<String, dynamic> map) {
    return VehicleConfig(
      id: map['id'],
      vehicleName: map['vehicleName'],
      brand: map['brand'],
      model: map['model'],
      year: map['year'],
      currentKilometers: map['currentKilometers'],
      lastKmUpdate: DateTime.fromMillisecondsSinceEpoch(map['lastKmUpdate']),
      licensePlate: map['licensePlate'],
      engineType: map['engineType'],
      isDefault: map['isDefault'] == 1,
    );
  }

  // Copier avec modifications
  VehicleConfig copyWith({
    int? id,
    String? vehicleName,
    String? brand,
    String? model,
    int? year,
    int? currentKilometers,
    DateTime? lastKmUpdate,
    String? licensePlate,
    String? engineType,
    bool? isDefault,
  }) {
    return VehicleConfig(
      id: id ?? this.id,
      vehicleName: vehicleName ?? this.vehicleName,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      year: year ?? this.year,
      currentKilometers: currentKilometers ?? this.currentKilometers,
      lastKmUpdate: lastKmUpdate ?? this.lastKmUpdate,
      licensePlate: licensePlate ?? this.licensePlate,
      engineType: engineType ?? this.engineType,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  @override
  String toString() {
    return 'VehicleConfig{vehicleName: $vehicleName, brand: $brand, model: $model, currentKm: $currentKilometers}';
  }
}
