import 'package:flutter/material.dart';
import 'lib/services/connectivity_service.dart';
import 'lib/widgets/smart_admob_banner.dart';

/// Test simple pour vérifier la détection de connectivité
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 Test de connectivité CAROSTI');
  print('================================');
  
  // Initialiser le service de connectivité
  final connectivityService = ConnectivityService();
  await connectivityService.initialize();
  
  print('✅ Service de connectivité initialisé');
  print('🌐 État initial: ${connectivityService.isConnected ? "Connecté" : "Déconnecté"}');
  
  // Écouter les changements
  connectivityService.connectivityStream.listen((isConnected) {
    print('🔄 Changement détecté: ${isConnected ? "Connecté ✅" : "Déconnecté ❌"}');
    print('📱 Les widgets AdMob seront ${isConnected ? "AFFICHÉS" : "MASQUÉS"}');
  });
  
  // Test manuel de connectivité
  print('\n🔍 Test manuel de connectivité...');
  final testResult = await connectivityService.testConnectivityWithTimeout();
  print('📊 Résultat test: ${testResult ? "Internet accessible ✅" : "Pas d\'internet ❌"}');
  
  print('\n🎯 Test terminé !');
  print('💡 Les widgets SmartAdMobBanner se masqueront automatiquement sans internet.');
  
  runApp(const TestApp());
}

class TestApp extends StatelessWidget {
  const TestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Connectivité CAROSTI',
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Test Connectivité'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '🧪 Test de connectivité',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Text(
                'Vérifiez la console pour les résultats',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 40),
              Text(
                '📱 Widget publicitaire intelligent :',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 20),
              // Widget publicitaire intelligent qui se masque sans internet
              SmartAdMobBanner(
                pageId: 'test',
                margin: EdgeInsets.all(16),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
