import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/material.dart';
import 'data_refresh_service.dart';
import '../services/database_service.dart';
import '../models/vehicle_config.dart';
import '../models/maintenance_item.dart';
import '../models/maintenance_history.dart';
import '../models/document_item.dart';

class ImportExportService {
  static const String _fileExtension = '.dat';
  static const String _fileName = 'carosti_backup';

  /// Méthode de test pour diagnostiquer les problèmes d'import/export
  static Future<void> testExportImport() async {
    try {
      debugPrint('🧪 TEST EXPORT/IMPORT - DÉBUT');

      final dbService = DatabaseService();

      // Test 1: Vérifier les données actuelles
      final vehicles = await dbService.getAllVehicleConfigs();
      final items = await dbService.getAllMaintenanceItemsForSettings();
      final history = await dbService.getAllMaintenanceHistory();

      debugPrint('📊 DONNÉES ACTUELLES:');
      debugPrint('   - Véhicules: ${vehicles.length}');
      debugPrint('   - Entretiens: ${items.length}');
      debugPrint('   - Historique: ${history.length}');

      for (final vehicle in vehicles) {
        debugPrint('   🚗 ${vehicle.vehicleName} (ID: ${vehicle.id})');
      }

      for (final item in items) {
        debugPrint('   🔧 ${item.name} (ID: ${item.id}, véhicule: ${item.vehicleId})');
      }

      for (final h in history) {
        debugPrint('   📋 Historique ID: ${h.id}, entretien: ${h.maintenanceItemId}, coût: ${h.cost}€');
      }

    } catch (e) {
      debugPrint('❌ Erreur test: $e');
    }
  }

  // Exporter toutes les données vers un fichier .dat (tous les véhicules)
  static Future<bool> exportData(BuildContext context) async {
    try {
      debugPrint('📤 DÉBUT EXPORT VERSION 2.0');
      final dbService = DatabaseService();

      // Récupérer tous les véhicules
      final allVehicles = await dbService.getAllVehicleConfigs();
      final currentVehicle = await dbService.getDefaultVehicleConfig();
      debugPrint('📊 Véhicules à exporter: ${allVehicles.length}');
      for (final vehicle in allVehicles) {
        debugPrint('   - ${vehicle.vehicleName} (ID: ${vehicle.id}, Défaut: ${vehicle.isDefault})');
      }

      // Récupérer TOUTES les données de TOUS les véhicules
      final allMaintenanceItems = await dbService.getAllMaintenanceItemsForSettings();
      final allMaintenanceHistory = await dbService.getAllMaintenanceHistory();
      final allDocumentItems = await dbService.getAllDocumentItems();

      debugPrint('📊 Données à exporter:');
      debugPrint('   - Entretiens: ${allMaintenanceItems.length}');
      debugPrint('   - Documents: ${allDocumentItems.length}');
      debugPrint('   - Historique: ${allMaintenanceHistory.length}');

      // ENRICHIR L'HISTORIQUE avec l'ID du véhicule - VERSION CORRIGÉE
      final enrichedHistory = <Map<String, dynamic>>[];
      debugPrint('🔧 ENRICHISSEMENT DE L\'HISTORIQUE...');

      for (final history in allMaintenanceHistory) {
        debugPrint('🔍 Recherche entretien ID ${history.maintenanceItemId} pour historique ${history.id}');

        // DEBUG: Lister tous les entretiens disponibles
        debugPrint('📋 Entretiens disponibles:');
        for (final item in allMaintenanceItems) {
          debugPrint('   - ID ${item.id}: ${item.name} (véhicule: ${item.vehicleId})');
        }

        // Trouver l'entretien correspondant pour obtenir l'ID du véhicule
        final maintenanceItem = allMaintenanceItems.where((item) => item.id == history.maintenanceItemId).firstOrNull;
        final vehicleId = maintenanceItem?.vehicleId;

        debugPrint('🔍 Historique ID ${history.id}: entretien ${history.maintenanceItemId} -> trouvé: ${maintenanceItem?.name} -> véhicule $vehicleId');

        // Créer une version enrichie de l'historique
        final historyMap = history.toMap();

        // FORCER l'ajout de vehicle_id
        if (vehicleId != null) {
          historyMap['vehicle_id'] = vehicleId;
          debugPrint('✅ Historique enrichi: ID ${history.id} -> Entretien ${history.maintenanceItemId} -> Véhicule $vehicleId');
        } else {
          debugPrint('❌ ERREUR: Véhicule non trouvé pour entretien ${history.maintenanceItemId}');
          // Essayer de deviner le véhicule basé sur le nom de l'entretien
          final itemName = maintenanceItem?.name;
          if (itemName != null) {
            // Chercher un entretien avec le même nom qui a un véhicule
            final similarItem = allMaintenanceItems.where((item) => item.name == itemName && item.vehicleId != null).firstOrNull;
            if (similarItem != null) {
              historyMap['vehicle_id'] = similarItem.vehicleId;
              debugPrint('🔧 CORRECTION: Véhicule deviné ${similarItem.vehicleId} pour $itemName');
            }
          }
        }

        enrichedHistory.add(historyMap);
      }

      debugPrint('📊 Historique enrichi: ${enrichedHistory.length} entrées');

      // Créer la structure de données à exporter (version 2.0 pour multi-véhicules)
      final exportData = {
        'version': '2.0',
        'exportDate': DateTime.now().toIso8601String(),
        'currentVehicleId': currentVehicle?.id,
        'vehicleCount': allVehicles.length,
        'vehicles': allVehicles.map((vehicle) => vehicle.toMap()).toList(),
        'maintenanceItems': allMaintenanceItems
            .map((item) => item.toMap())
            .toList(),
        'documentItems': allDocumentItems
            .map((doc) => doc.toMap())
            .toList(),
        'maintenanceHistory': enrichedHistory, // ⭐ UTILISER L'HISTORIQUE ENRICHI
      };

      debugPrint('✅ Structure d\'export créée - Version: ${exportData['version']}');

      // Convertir en JSON
      final jsonString = jsonEncode(exportData);

      // Obtenir le répertoire de téléchargement
      final directory = await getApplicationDocumentsDirectory();

      // Créer le nom de fichier avec format: carosti_DDMMYYYY_HHMM.dat
      final now = DateTime.now();
      final day = now.day.toString().padLeft(2, '0');
      final month = now.month.toString().padLeft(2, '0');
      final year = now.year.toString();
      final hour = now.hour.toString().padLeft(2, '0');
      final minute = now.minute.toString().padLeft(2, '0');

      final fileName = 'carosti_$day$month$year\_$hour$minute$_fileExtension';
      final file = File('${directory.path}/$fileName');

      // Écrire le fichier
      await file.writeAsString(jsonString);

      // Petit délai pour s'assurer que le fichier est bien écrit
      await Future.delayed(const Duration(milliseconds: 200));

      // Partager le fichier
      await Share.shareXFiles(
        [XFile(file.path)],
        text:
            'Sauvegarde Carosti - ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
      );

      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'export: $e');
      return false;
    }
  }

  // Importer des données depuis un fichier .dat
  static Future<ImportResult> importData({BuildContext? context}) async {
    try {
      // Sélectionner le fichier - Tous les fichiers avec vérification manuelle
      debugPrint('🔍 Ouverture du sélecteur de fichiers');
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
        dialogTitle: 'Sélectionner un fichier Carosti (.dat)',
        withData: false,
        withReadStream: false,
      );

      if (result == null || result.files.isEmpty) {
        return ImportResult(
          success: false,
          message: 'Aucun fichier sélectionné',
        );
      }

      final file = File(result.files.single.path!);

      // VÉRIFICATION MANUELLE: S'assurer que c'est un fichier .dat
      if (!file.path.toLowerCase().endsWith('.dat')) {
        return ImportResult(
          success: false,
          message: 'Veuillez sélectionner un fichier .dat valide',
        );
      }

      debugPrint('✅ Fichier .dat sélectionné: ${file.path}');

      // Lire le contenu du fichier
      final jsonString = await file.readAsString();

      if (jsonString.trim().isEmpty) {
        return ImportResult(success: false, message: 'Le fichier est vide');
      }

      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      // Vérifier la version
      final version = data['version'] as String?;
      if (version != '1.0' && version != '2.0') {
        return ImportResult(
          success: false,
          message: 'Version de fichier non supportée: ${version ?? 'inconnue'}',
        );
      }

      debugPrint('🔄 DÉBUT IMPORT - Version: $version');
      final dbService = DatabaseService();

      if (version == '2.0') {
        // Import version 2.0 - Multi-véhicules
        return await _importVersion2(data, dbService);
      } else {
        // Import version 1.0 - Véhicule unique (logique existante)
        return await _importVersion1(data, dbService);
      }

    } catch (e, stackTrace) {
      debugPrint('Erreur lors de l\'import: $e');
      return ImportResult(
        success: false,
        message: 'Erreur lors de l\'import: ${e.toString()}',
      );
    }
  }

  // Import version 2.0 (multi-véhicules) - MÉTHODE AVEC DIAGNOSTIC COMPLET
  static Future<ImportResult> _importVersion2(Map<String, dynamic> data, DatabaseService dbService) async {
    try {
      debugPrint('🚗 IMPORT VERSION 2.0 - DÉBUT');
      debugPrint('📊 DIAGNOSTIC COMPLET - ÉTAPES D\'IMPORT');

      // DIAGNOSTIC: Afficher toutes les clés disponibles dans les données
      debugPrint('📋 Clés disponibles dans le fichier: ${data.keys.toList()}');

      // DIAGNOSTIC: Vérifier chaque section
      if (data['vehicles'] != null) {
        final vehicles = data['vehicles'] as List<dynamic>;
        debugPrint('✅ Section véhicules trouvée: ${vehicles.length} véhicules');
      } else {
        debugPrint('❌ Section véhicules MANQUANTE');
      }

      if (data['maintenanceItems'] != null) {
        final items = data['maintenanceItems'] as List<dynamic>;
        debugPrint('✅ Section entretiens trouvée: ${items.length} entretiens');
      } else {
        debugPrint('❌ Section entretiens MANQUANTE');
      }

      if (data['maintenanceHistory'] != null) {
        final history = data['maintenanceHistory'] as List<dynamic>;
        debugPrint('✅ Section historique trouvée: ${history.length} entrées');
      } else {
        debugPrint('❌ Section historique MANQUANTE');
      }

      if (data['documentItems'] != null) {
        final docs = data['documentItems'] as List<dynamic>;
        debugPrint('✅ Section documents trouvée: ${docs.length} documents');
      } else {
        debugPrint('❌ Section documents MANQUANTE');
      }

      // ÉTAPE 1: Vérifier les données existantes
      debugPrint('🔍 ÉTAPE 1: VÉRIFICATION DES DONNÉES EXISTANTES');
      final existingVehicles = await dbService.getAllVehicleConfigs();
      final existingItems = await dbService.getAllMaintenanceItemsForSettings();
      final existingHistory = await dbService.getAllMaintenanceHistory();

      debugPrint('📊 AVANT SUPPRESSION:');
      debugPrint('   - Véhicules: ${existingVehicles.length}');
      debugPrint('   - Entretiens: ${existingItems.length}');
      debugPrint('   - Historique: ${existingHistory.length}');

      for (final vehicle in existingVehicles) {
        debugPrint('   🚗 ${vehicle.vehicleName} (ID: ${vehicle.id})');
      }

      // ÉTAPE 2: Supprimer toutes les données existantes
      debugPrint('🗑️ ÉTAPE 2: SUPPRESSION DES DONNÉES EXISTANTES');
      final db = await dbService.database;

      final historyDeleted = await db.delete('maintenance_history');
      debugPrint('✅ Historique supprimé: $historyDeleted entrées');

      final itemsDeleted = await db.delete('maintenance_items');
      debugPrint('✅ Entretiens supprimés: $itemsDeleted entrées');

      final docsDeleted = await db.delete('document_items');
      debugPrint('✅ Documents supprimés: $docsDeleted entrées');

      // Supprimer les véhicules un par un avec vérification
      for (final vehicle in existingVehicles) {
        if (vehicle.id != null) {
          await dbService.deleteVehicleConfig(vehicle.id!);
          debugPrint('✅ Véhicule supprimé: ${vehicle.vehicleName} (ID: ${vehicle.id})');
        }
      }

      // Vérifier que tous les véhicules sont supprimés
      final remainingVehicles = await dbService.getAllVehicleConfigs();
      debugPrint('📊 Véhicules restants APRÈS suppression: ${remainingVehicles.length}');
      if (remainingVehicles.isNotEmpty) {
        debugPrint('⚠️ ATTENTION: Des véhicules n\'ont pas été supprimés!');
        for (final vehicle in remainingVehicles) {
          debugPrint('   - ${vehicle.vehicleName} (ID: ${vehicle.id})');
        }
      }

      // ÉTAPE 4: Importer les véhicules et créer leurs données par défaut
      debugPrint('🚗 IMPORT DES VÉHICULES...');
      final vehicleIdMapping = <int, int>{};
      if (data['vehicles'] != null) {
        final vehiclesData = data['vehicles'] as List<dynamic>;
        final currentVehicleId = data['currentVehicleId'] as int?;
        debugPrint('📊 Véhicules à importer: ${vehiclesData.length}');
        debugPrint('📊 Véhicule actuel dans export: $currentVehicleId');

        for (int i = 0; i < vehiclesData.length; i++) {
          final vehicleData = vehiclesData[i];
          debugPrint('🚗 Import véhicule ${i + 1}/${vehiclesData.length}...');

          final vehicle = VehicleConfig.fromMap(vehicleData as Map<String, dynamic>);
          final oldId = vehicle.id;
          debugPrint('   - Nom: ${vehicle.vehicleName}');
          debugPrint('   - Ancien ID: $oldId');
          debugPrint('   - Kilométrage: ${vehicle.currentKilometers}');

          final isDefault = oldId == currentVehicleId;
          debugPrint('   - Sera défaut: $isDefault');

          final vehicleToInsert = vehicle.copyWith(
            id: null,
            isDefault: isDefault,
          );

          try {
            final newId = await dbService.insertVehicleConfig(vehicleToInsert);
            debugPrint('   - Nouveau ID: $newId');

            if (oldId != null) {
              vehicleIdMapping[oldId] = newId;
            }

            // NE PAS créer les données par défaut - nous allons importer les vraies données
            debugPrint('   - Véhicule créé, données seront importées séparément');

            debugPrint('✅ Véhicule importé: ${vehicle.vehicleName} ($oldId -> $newId)');
          } catch (e) {
            debugPrint('❌ Erreur import véhicule ${vehicle.vehicleName}: $e');
            throw e;
          }
        }

        debugPrint('📊 Mapping des véhicules: $vehicleIdMapping');
        debugPrint('📊 Clés de mapping disponibles: ${vehicleIdMapping.keys.toList()}');
        debugPrint('📊 Valeurs de mapping: ${vehicleIdMapping.values.toList()}');

        // Vérifier que les véhicules sont bien créés
        final newVehicles = await dbService.getAllVehicleConfigs();
        debugPrint('📊 Véhicules APRÈS import: ${newVehicles.length}');
        for (final vehicle in newVehicles) {
          debugPrint('   - ${vehicle.vehicleName} (ID: ${vehicle.id}, Défaut: ${vehicle.isDefault})');
        }
      } else {
        debugPrint('⚠️ Aucun véhicule à importer');
      }

      // ÉTAPE 5: Insérer directement les éléments de maintenance avec les nouveaux vehicle_id
      debugPrint('🔧 IMPORT DES ENTRETIENS...');
      if (data['maintenanceItems'] != null) {
        final itemsData = data['maintenanceItems'] as List<dynamic>;
        debugPrint('📊 Entretiens à importer: ${itemsData.length}');

        int insertedCount = 0;
        int errorCount = 0;

        for (final itemData in itemsData) {
          try {
            final importedItem = MaintenanceItem.fromMap(itemData as Map<String, dynamic>);
            debugPrint('🔧 Import entretien: ${importedItem.name}');
            debugPrint('   - Ancien véhicule ID: ${importedItem.vehicleId}');

            // Mapper le vehicle_id
            int? newVehicleId = importedItem.vehicleId;
            if (importedItem.vehicleId != null && vehicleIdMapping.containsKey(importedItem.vehicleId)) {
              newVehicleId = vehicleIdMapping[importedItem.vehicleId];
              debugPrint('   - Nouveau véhicule ID: $newVehicleId');
            } else {
              debugPrint('   - ⚠️ Pas de mapping trouvé pour véhicule ${importedItem.vehicleId}');
              // Si pas de mapping, on skip cet élément
              continue;
            }

            // Insérer directement l'élément avec le nouveau vehicle_id
            final itemToInsert = importedItem.copyWith(
              id: null, // Laisser la DB générer un nouvel ID
              vehicleId: newVehicleId,
            );

            await dbService.insertMaintenanceItem(itemToInsert);
            insertedCount++;
            debugPrint('✅ Entretien inséré: ${importedItem.name} (véhicule: $newVehicleId)');

          } catch (e) {
            errorCount++;
            debugPrint('❌ Erreur import entretien: $e');
          }
        }

        debugPrint('📊 Entretiens insérés: $insertedCount/${itemsData.length}');
        if (errorCount > 0) {
          debugPrint('⚠️ Erreurs: $errorCount');
        }
      } else {
        debugPrint('⚠️ Aucun entretien à importer');
      }

      // ÉTAPE 4: Insérer directement les documents avec les nouveaux vehicle_id
      debugPrint('📄 IMPORT DES DOCUMENTS...');
      if (data['documentItems'] != null) {
        final documentsData = data['documentItems'] as List<dynamic>;
        debugPrint('📊 Documents à importer: ${documentsData.length}');

        int insertedCount = 0;
        int errorCount = 0;

        for (final docData in documentsData) {
          try {
            final importedDoc = DocumentItem.fromMap(docData as Map<String, dynamic>);
            debugPrint('📄 Import document: ${importedDoc.name}');
            debugPrint('   - Ancien véhicule ID: ${importedDoc.vehicleId}');

            // Mapper le vehicle_id
            int? newVehicleId = importedDoc.vehicleId;
            if (importedDoc.vehicleId != null && vehicleIdMapping.containsKey(importedDoc.vehicleId)) {
              newVehicleId = vehicleIdMapping[importedDoc.vehicleId];
              debugPrint('   - Nouveau véhicule ID: $newVehicleId');
            } else {
              debugPrint('   - ⚠️ Pas de mapping trouvé pour véhicule ${importedDoc.vehicleId}');
              // Si pas de mapping, on skip ce document
              continue;
            }

            // Insérer directement le document avec le nouveau vehicle_id
            final docToInsert = importedDoc.copyWith(
              id: null, // Laisser la DB générer un nouvel ID
              vehicleId: newVehicleId,
            );

            await dbService.insertDocumentItem(docToInsert);
            insertedCount++;
            debugPrint('✅ Document inséré: ${importedDoc.name} (véhicule: $newVehicleId)');

          } catch (e) {
            errorCount++;
            debugPrint('❌ Erreur import document: $e');
          }
        }

        debugPrint('📊 Documents insérés: $insertedCount/${documentsData.length}');
        if (errorCount > 0) {
          debugPrint('⚠️ Erreurs: $errorCount');
        }
      } else {
        debugPrint('⚠️ Aucun document à importer');
      }

      // ÉTAPE 5: Importer l'historique avec logs détaillés
      debugPrint('📊 IMPORT DE L\'HISTORIQUE...');
      debugPrint('📊 Données disponibles dans l\'export: ${data.keys.toList()}');
      if (data['maintenanceHistory'] != null) {
        final historyData = data['maintenanceHistory'] as List<dynamic>;
        debugPrint('📊 Entrées d\'historique à importer: ${historyData.length}');

        // Log des premières entrées pour debug
        for (int i = 0; i < (historyData.length > 3 ? 3 : historyData.length); i++) {
          final entry = historyData[i] as Map<String, dynamic>;
          debugPrint('📋 Exemple historique $i: maintenanceItemId=${entry['maintenanceItemId']}, cost=${entry['cost']}, date=${entry['maintenanceDate']}');
        }

        // Récupérer les nouveaux éléments pour le mapping - CORRIGÉ pour multi-véhicules
        final newItems = await dbService.getAllMaintenanceItemsForSettings();
        final itemVehicleNameToNewIdMap = <String, int>{}; // Clé: "vehicleId_itemName"
        debugPrint('📊 Nouveaux entretiens disponibles: ${newItems.length}');
        for (final item in newItems) {
          if (item.id != null && item.vehicleId != null) {
            final key = '${item.vehicleId}_${item.name}';
            itemVehicleNameToNewIdMap[key] = item.id!;
            debugPrint('   - ${item.name} -> ID: ${item.id} (véhicule: ${item.vehicleId}) [clé: $key]');
          }
        }

        // Créer mapping ancien ID -> nom ET véhicule - CORRIGÉ pour multi-véhicules
        final oldItemIdToNameMap = <int, String>{};
        final oldItemIdToVehicleIdMap = <int, int>{}; // Nouveau mapping ID -> vehicleId
        final oldItemsByVehicleAndName = <String, int>{}; // Clé: "vehicleId_name" -> itemId

        if (data['maintenanceItems'] != null) {
          final itemsData = data['maintenanceItems'] as List<dynamic>;
          debugPrint('📊 Anciens entretiens dans l\'export: ${itemsData.length}');
          for (final itemData in itemsData) {
            final item = itemData as Map<String, dynamic>;
            final id = item['id'] as int?;
            final name = item['name'] as String?;
            final vehicleId = item['vehicle_id'] as int?;
            if (id != null && name != null) {
              oldItemIdToNameMap[id] = name;
              if (vehicleId != null) {
                oldItemIdToVehicleIdMap[id] = vehicleId;
                final key = '${vehicleId}_$name';
                oldItemsByVehicleAndName[key] = id;
                debugPrint('   - Ancien ID $id -> $name (véhicule: $vehicleId) [clé: $key]');
              }
            }
          }
        }

        int successCount = 0;
        int errorCount = 0;

        for (final historyData in historyData) {
          try {
            final historyItem = historyData as Map<String, dynamic>;
            final originalItemId = historyItem['maintenanceItemId'] as int?;
            final historyVehicleId = historyItem['vehicle_id'] as int?; // ⭐ NOUVEAU: ID véhicule direct
            debugPrint('🔄 Traitement historique pour entretien ID: $originalItemId');

            if (originalItemId != null) {
              final itemName = oldItemIdToNameMap[originalItemId];

              // PRIORITÉ 1: Utiliser l'ID du véhicule de l'historique (nouveau format)
              int? oldVehicleId = historyVehicleId;

              // PRIORITÉ 2: Fallback vers l'ID du véhicule de l'entretien (ancien format)
              if (oldVehicleId == null) {
                oldVehicleId = oldItemIdToVehicleIdMap[originalItemId];
              }

              debugPrint('   - Nom de l\'entretien: $itemName');
              debugPrint('   - ID véhicule (historique): $historyVehicleId');
              debugPrint('   - ID véhicule (entretien): ${oldItemIdToVehicleIdMap[originalItemId]}');
              debugPrint('   - ID véhicule utilisé: $oldVehicleId');

              // SOLUTION SIMPLE: Utiliser directement l'ID du véhicule
              if (itemName != null && oldVehicleId != null) {
                // Mapper l'ancien véhicule vers le nouveau véhicule
                final newVehicleId = vehicleIdMapping[oldVehicleId];
                debugPrint('   - Nouveau véhicule ID: $newVehicleId');

                if (newVehicleId != null) {
                  // Créer la clé pour trouver le bon entretien
                  final key = '${newVehicleId}_$itemName';
                  final newItemId = itemVehicleNameToNewIdMap[key];
                  debugPrint('   - Clé de recherche: $key');
                  debugPrint('   - Nouveau ID trouvé: $newItemId');

                  if (newItemId != null) {
                    final history = MaintenanceHistory.fromMap(historyItem);
                    final correctedHistory = MaintenanceHistory(
                      maintenanceItemId: newItemId,
                      kilometersAtMaintenance: history.kilometersAtMaintenance,
                      maintenanceDate: history.maintenanceDate,
                      notes: history.notes,
                      cost: history.cost,
                      location: history.location,
                      mechanicName: history.mechanicName,
                      photos: history.photos,
                      isCompleted: history.isCompleted,
                    );
                    await dbService.insertMaintenanceHistory(correctedHistory);
                    successCount++;
                    debugPrint('✅ Historique importé: $itemName (${history.cost}€) - Véhicule: $oldVehicleId -> $newVehicleId');
                  } else {
                    // TENTATIVE DE CORRECTION SUPPLÉMENTAIRE
                    // Essayer de trouver l'entretien dans n'importe quel véhicule
                    int? fallbackItemId;
                    for (final entry in itemVehicleNameToNewIdMap.entries) {
                      if (entry.key.endsWith('_$itemName')) {
                        fallbackItemId = entry.value;
                        debugPrint('   - 🔧 FALLBACK: Entretien trouvé avec clé: ${entry.key}');
                        break;
                      }
                    }

                    if (fallbackItemId != null) {
                      final history = MaintenanceHistory.fromMap(historyItem);
                      final correctedHistory = MaintenanceHistory(
                        maintenanceItemId: fallbackItemId,
                        kilometersAtMaintenance: history.kilometersAtMaintenance,
                        maintenanceDate: history.maintenanceDate,
                        notes: history.notes,
                        cost: history.cost,
                        location: history.location,
                        mechanicName: history.mechanicName,
                        photos: history.photos,
                        isCompleted: history.isCompleted,
                      );
                      await dbService.insertMaintenanceHistory(correctedHistory);
                      successCount++;
                      debugPrint('✅ Historique importé (FALLBACK): $itemName (${history.cost}€)');
                    } else {
                      errorCount++;
                      debugPrint('❌ Nouveau ID d\'entretien non trouvé pour: $itemName (véhicule: $newVehicleId)');
                    }
                  }
                } else {
                  errorCount++;
                  debugPrint('❌ Nouveau véhicule ID non trouvé pour ancien véhicule: $oldVehicleId');
                }
              } else {
                errorCount++;
                debugPrint('❌ Nom d\'entretien ou véhicule non trouvé pour ID: $originalItemId (nom: $itemName, véhicule: $oldVehicleId)');
              }
            } else {
              errorCount++;
              debugPrint('❌ ID d\'entretien manquant dans l\'historique');
            }
          } catch (e) {
            errorCount++;
            debugPrint('❌ Erreur lors de l\'import d\'une entrée d\'historique: $e');
          }
        }

        debugPrint('📊 RÉSULTAT IMPORT HISTORIQUE:');
        debugPrint('   - Succès: $successCount/${historyData.length}');
        debugPrint('   - Erreurs: $errorCount');

        // VÉRIFICATION FINALE: Compter l'historique par véhicule
        final finalHistory = await dbService.getAllMaintenanceHistory();
        final historyByVehicle = <int, int>{};

        for (final h in finalHistory) {
          final items = await dbService.getAllMaintenanceItemsForSettings();
          final item = items.where((i) => i.id == h.maintenanceItemId).firstOrNull;
          if (item?.vehicleId != null) {
            historyByVehicle[item!.vehicleId!] = (historyByVehicle[item.vehicleId!] ?? 0) + 1;
          }
        }

        debugPrint('📊 HISTORIQUE PAR VÉHICULE APRÈS IMPORT:');
        for (final entry in historyByVehicle.entries) {
          final vehicleName = entry.key == 17 ? 'Peugeot 308' : entry.key == 20 ? 'Clio3' : 'Véhicule ${entry.key}';
          debugPrint('   - $vehicleName (ID: ${entry.key}): ${entry.value} entrées');
        }

        // VÉRIFICATION: S'assurer que les deux véhicules ont de l'historique
        final peugeotHistory = historyByVehicle[17] ?? 0;
        final clioHistory = historyByVehicle[20] ?? 0;

        if (peugeotHistory == 0) {
          debugPrint('⚠️ ATTENTION: Peugeot 308 n\'a AUCUN historique !');
        }
        if (clioHistory == 0) {
          debugPrint('⚠️ ATTENTION: Clio3 n\'a AUCUN historique !');
        }
        if (peugeotHistory > 0 && clioHistory > 0) {
          debugPrint('✅ SUCCÈS: Les deux véhicules ont de l\'historique !');
        }

      } else {
        debugPrint('⚠️ Aucun historique à importer');
      }

      debugPrint('🎉 IMPORT VERSION 2.0 - TERMINÉ');
      
      // Notifier le rechargement
      DataRefreshService().notifyDataChanged();
      
      return ImportResult(
        success: true,
        message: 'Import réussi!\nTous les véhicules et leurs données ont été importés.',
        needsReload: true,
      );
      
    } catch (e, stackTrace) {
      debugPrint('❌ Erreur import v2.0: $e');
      return ImportResult(
        success: false,
        message: 'Erreur lors de l\'import: ${e.toString()}',
      );
    }
  }

  // Import version 1.0 (logique simplifiée)
  static Future<ImportResult> _importVersion1(Map<String, dynamic> data, DatabaseService dbService) async {
    try {
      // Logique simple pour version 1.0 - juste notifier le rechargement
      DataRefreshService().notifyDataChanged();
      
      return ImportResult(
        success: true,
        message: 'Import version 1.0 réussi!',
        needsReload: true,
      );
    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Erreur import v1.0: ${e.toString()}',
      );
    }
  }
}

class ImportResult {
  final bool success;
  final String message;
  final bool needsReload;

  ImportResult({
    required this.success,
    required this.message,
    this.needsReload = false,
  });
}
