import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../models/vehicle_config.dart';

class VehicleConfigScreen extends StatefulWidget {
  final bool isCreatingNew;

  const VehicleConfigScreen({
    super.key,
    this.isCreatingNew = false,
  });

  @override
  State<VehicleConfigScreen> createState() => _VehicleConfigScreenState();
}

class _VehicleConfigScreenState extends State<VehicleConfigScreen> {
  final _formKey = GlobalKey<FormState>();
  final _vehicleNameController = TextEditingController();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _yearController = TextEditingController();
  final _licensePlateController = TextEditingController();
  final _engineTypeController = TextEditingController();
  final _kilometersController = TextEditingController();

  bool _isLoading = false;
  bool _isEditing = false;

  // Variables pour gérer l'état du focus et les valeurs initiales
  final Map<String, bool> _fieldHasFocus = {};
  final Map<String, String> _initialValues = {};
  final Map<String, FocusNode> _focusNodes = {};

  @override
  void initState() {
    super.initState();
    _initializeFocusNodes();
    _loadCurrentVehicle();
  }

  void _initializeFocusNodes() {
    final fields = [
      'vehicleName',
      'brand',
      'model',
      'year',
      'licensePlate',
      'engineType',
      'kilometers',
    ];
    for (String field in fields) {
      _focusNodes[field] = FocusNode();
      _fieldHasFocus[field] = false;
      _initialValues[field] = '';

      _focusNodes[field]!.addListener(() {
        setState(() {
          _fieldHasFocus[field] = _focusNodes[field]!.hasFocus;
        });
      });
    }
  }

  void _loadCurrentVehicle() {
    final provider = context.read<MaintenanceProvider>();
    final vehicle = widget.isCreatingNew ? null : provider.currentVehicle;

    if (vehicle != null && !widget.isCreatingNew) {
      _isEditing = true;
      _vehicleNameController.text = vehicle.vehicleName;
      _brandController.text = vehicle.brand;
      _modelController.text = vehicle.model;
      _yearController.text = vehicle.year.toString();
      _licensePlateController.text = vehicle.licensePlate ?? '';
      _engineTypeController.text = vehicle.engineType ?? '';
      _kilometersController.text = vehicle.currentKilometers.toString();

      // Sauvegarder les valeurs initiales
      _initialValues['vehicleName'] = vehicle.vehicleName;
      _initialValues['brand'] = vehicle.brand;
      _initialValues['model'] = vehicle.model;
      _initialValues['year'] = vehicle.year.toString();
      _initialValues['licensePlate'] = vehicle.licensePlate ?? '';
      _initialValues['engineType'] = vehicle.engineType ?? '';
      _initialValues['kilometers'] = vehicle.currentKilometers.toString();
    } else {
      // Mode création ou aucun véhicule existant
      _isEditing = false;
      _yearController.text = DateTime.now().year.toString();
      _initialValues['year'] = DateTime.now().year.toString();

      if (widget.isCreatingNew) {
        _kilometersController.text = '0';
        _initialValues['kilometers'] = '0';
      }
    }
  }

  @override
  void dispose() {
    _vehicleNameController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _yearController.dispose();
    _licensePlateController.dispose();
    _engineTypeController.dispose();
    _kilometersController.dispose();

    // Nettoyer les focus nodes
    for (FocusNode node in _focusNodes.values) {
      node.dispose();
    }

    super.dispose();
  }

  // Vérifier si un champ a changé par rapport à sa valeur initiale
  bool _hasFieldChanged(String fieldName, String currentValue) {
    return currentValue != _initialValues[fieldName];
  }

  // Vérifier si le champ doit afficher le texte de signalisation
  bool _shouldShowPlaceholder(String fieldName, String currentValue) {
    return !_fieldHasFocus[fieldName]! &&
        (currentValue.isEmpty || !_hasFieldChanged(fieldName, currentValue));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.isCreatingNew
              ? 'Nouveau véhicule'
              : (_isEditing ? 'Modifier véhicule' : 'Configuration véhicule'),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              onPressed: _isLoading ? null : _showDeleteDialog,
              tooltip: 'Supprimer véhicule',
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête avec icône
              Center(
                child: Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    MdiIcons.car,
                    size: 36,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Informations de base
              Text(
                'Informations',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 12),

              Card(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _vehicleNameController,
                        focusNode: _focusNodes['vehicleName'],
                        decoration: InputDecoration(
                          labelText: 'Nom *',
                          hintText:
                              _shouldShowPlaceholder(
                                'vehicleName',
                                _vehicleNameController.text,
                              )
                              ? 'Voiture'
                              : null,
                          prefixIcon: Icon(MdiIcons.carInfo, size: 20),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Nom requis';
                          }
                          return null;
                        },
                        onChanged: (value) =>
                            setState(() {}), // Pour rafraîchir l'affichage
                      ),

                      const SizedBox(height: 16),

                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _brandController,
                              decoration: InputDecoration(
                                labelText: 'Marque *',
                                hintText: 'Peugeot',
                                prefixIcon: Icon(MdiIcons.carEstate, size: 20),
                                border: const OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Marque requise';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextFormField(
                              controller: _modelController,
                              decoration: InputDecoration(
                                labelText: 'Modèle *',
                                hintText: '308',
                                prefixIcon: Icon(MdiIcons.carSide, size: 20),
                                border: const OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Modèle requis';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _yearController,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(4),
                              ],
                              decoration: InputDecoration(
                                labelText: 'Année *',
                                hintText: '2020',
                                prefixIcon: Icon(MdiIcons.calendar, size: 20),
                                border: const OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Année requise';
                                }
                                final year = int.tryParse(value);
                                if (year == null ||
                                    year < 1900 ||
                                    year > DateTime.now().year + 1) {
                                  return 'Année invalide';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextFormField(
                              controller: _licensePlateController,
                              decoration: InputDecoration(
                                labelText: 'Plaque',
                                hintText: 'AB-123',
                                prefixIcon: Icon(
                                  MdiIcons.cardAccountDetailsOutline,
                                  size: 20,
                                ),
                                border: const OutlineInputBorder(),
                              ),
                              textCapitalization: TextCapitalization.characters,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Informations techniques
              Text(
                'Technique',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 12),

              Card(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _engineTypeController,
                        decoration: InputDecoration(
                          labelText: 'Moteur',
                          hintText: 'Essence',
                          prefixIcon: Icon(MdiIcons.engine, size: 20),
                          border: const OutlineInputBorder(),
                        ),
                      ),

                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _kilometersController,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        decoration: InputDecoration(
                          labelText: 'Km *',
                          hintText: '50000',
                          suffixText: 'km',
                          prefixIcon: Icon(MdiIcons.speedometer, size: 20),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Km requis';
                          }
                          final km = int.tryParse(value);
                          if (km == null || km < 0) {
                            return 'Km invalide';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Boutons d'action
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _saveVehicle,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.save, size: 18),
                  label: Text(
                    _isLoading
                        ? 'Sauvegarde...'
                        : (_isEditing ? 'Modifier' : 'Ajouter'),
                    style: const TextStyle(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),

              if (_isEditing) ...[
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: _isLoading ? null : _showDeleteDialog,
                    icon: const Icon(Icons.delete, color: Colors.red, size: 18),
                    label: const Text(
                      'Supprimer',
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      side: const BorderSide(color: Colors.red),
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 20),

              // Note d'information
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[700], size: 16),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Champs * requis.',
                        style: TextStyle(color: Colors.blue[700], fontSize: 10),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveVehicle() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final vehicle = VehicleConfig(
        id: _isEditing
            ? context.read<MaintenanceProvider>().currentVehicle?.id
            : null,
        vehicleName: _vehicleNameController.text.trim(),
        brand: _brandController.text.trim(),
        model: _modelController.text.trim(),
        year: int.parse(_yearController.text),
        currentKilometers: int.parse(_kilometersController.text),
        lastKmUpdate: DateTime.now(),
        licensePlate: _licensePlateController.text.trim().isEmpty
            ? null
            : _licensePlateController.text.trim(),
        engineType: _engineTypeController.text.trim().isEmpty
            ? null
            : _engineTypeController.text.trim(),
        isDefault: widget.isCreatingNew ? false : true, // Nouveau véhicule n'est pas défaut par défaut
      );

      if (_isEditing) {
        await context.read<MaintenanceProvider>().updateVehicleConfig(vehicle);
      } else {
        await context.read<MaintenanceProvider>().createVehicle(vehicle);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditing ? 'Véhicule modifié' : 'Véhicule ajouté'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          '⚠️ Supprimer véhicule',
          style: TextStyle(fontSize: 16),
        ),
        content: const Text(
          'Cette action supprimera DÉFINITIVEMENT :\n\n'
          '• Le véhicule et sa configuration\n'
          '• Tout l\'historique des entretiens\n'
          '• Toutes les données de l\'application\n\n'
          'Cette action est IRRÉVERSIBLE !',
          style: TextStyle(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler', style: TextStyle(fontSize: 12)),
          ),
          ElevatedButton(
            onPressed: _isLoading
                ? null
                : () async {
                    Navigator.pop(context);
                    await _deleteVehicleAndAllData();
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer', style: TextStyle(fontSize: 12)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteVehicleAndAllData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final provider = Provider.of<MaintenanceProvider>(context, listen: false);
      final currentVehicle = provider.currentVehicle;
      final allVehicles = provider.allVehicles;

      if (currentVehicle == null) {
        throw Exception('Aucun véhicule actuel trouvé');
      }

      debugPrint('🗑️ Suppression véhicule: ${currentVehicle.vehicleName}');
      debugPrint('📊 Nombre total de véhicules: ${allVehicles.length}');

      if (allVehicles.length > 1) {
        // CAS 1: Plusieurs véhicules → Supprimer seulement le véhicule actuel + ses données
        debugPrint('🔄 CAS 1: Suppression du véhicule actuel uniquement');
        await provider.deleteVehicle(currentVehicle);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Véhicule "${currentVehicle.vehicleName}" supprimé'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // CAS 2: Un seul véhicule → Supprimer + créer véhicule par défaut vide
        debugPrint('🔄 CAS 2: Suppression + création véhicule par défaut vide');

        // Supprimer le véhicule actuel et toutes ses données
        await provider.deleteVehicle(currentVehicle);

        // Créer un nouveau véhicule par défaut vide
        final defaultVehicle = VehicleConfig(
          vehicleName: 'Mon Véhicule',
          brand: '',
          model: '',
          year: DateTime.now().year,
          currentKilometers: 0,
          lastKmUpdate: DateTime.now(),
          licensePlate: null,
          engineType: null,
          isDefault: true,
        );

        await provider.createVehicle(defaultVehicle);
        debugPrint('✅ Véhicule par défaut vide créé');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Véhicule supprimé et véhicule par défaut créé'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      if (mounted) {
        // Retourner à l'écran principal
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    } catch (e) {
      debugPrint('❌ Erreur suppression véhicule: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la suppression: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
