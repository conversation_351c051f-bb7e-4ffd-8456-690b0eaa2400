import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../services/theme_service.dart';
import '../services/currency_service.dart';
import '../services/import_export_service.dart';
import 'vehicle_config_screen.dart';
import 'vehicle_management_screen.dart';
import 'maintenance_intervals_screen.dart';
import 'about_screen.dart';
// import 'data_management_screen.dart'; // Temporairement désactivé
import 'theme_settings_screen.dart';
import 'currency_settings_screen.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_animations.dart';
import '../widgets/shimmer_loading.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Paramètres',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer<MaintenanceProvider>(
        builder: (context, provider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Section Véhicule
              _buildSectionHeader(context, 'Véhicule', MdiIcons.car),
              const SizedBox(height: 12),

              _buildSettingsTile(
                context,
                title: 'Config véhicule',
                subtitle:
                    provider.currentVehicle?.vehicleName ?? 'Aucun véhicule',
                icon: MdiIcons.carCog,
                onTap: () => _navigateToVehicleConfig(context),
              ),

              const SizedBox(height: 8),

              _buildSettingsTile(
                context,
                title: 'Mes véhicules',
                subtitle: '${provider.allVehicles.length} véhicule(s) configuré(s)',
                icon: MdiIcons.carMultiple,
                onTap: () => _navigateToVehicleManagement(context),
              ),

              const SizedBox(height: 24),

              // Section Entretien
              _buildSectionHeader(context, 'Entretien', MdiIcons.wrench),
              const SizedBox(height: 12),

              _buildSettingsTile(
                context,
                title: 'Intervalles',
                subtitle: 'Intervalles défaut et personnalisés',
                icon: MdiIcons.clockOutline,
                onTap: () => _navigateToMaintenanceIntervals(context),
              ),

              _buildSettingsTile(
                context,
                title: 'Notifications',
                subtitle: 'Rappels d\'entretien',
                icon: MdiIcons.bell,
                onTap: () =>
                    _showNotImplementedDialog(context, 'Notifications'),
              ),

              const SizedBox(height: 24),

              // Section Données
              _buildSectionHeader(context, 'Données', MdiIcons.database),
              const SizedBox(height: 12),

              _buildSettingsTile(
                context,
                title: 'Import/Export',
                subtitle: 'Sauvegarde et restauration',
                icon: Icons.import_export,
                onTap: () => _showImportExport(context),
              ),

              const SizedBox(height: 24),

              // Section Application
              _buildSectionHeader(context, 'Application', MdiIcons.application),
              const SizedBox(height: 12),

              _buildSettingsTile(
                context,
                title: 'Thème',
                subtitle: 'Couleurs et apparence',
                icon: MdiIcons.palette,
                onTap: () => _navigateToThemeSettings(context),
              ),

              _buildSettingsTile(
                context,
                title: 'Devise',
                subtitle: 'Dollar, Euro, DZD',
                icon: Icons.monetization_on,
                onTap: () => _navigateToCurrencySettings(context),
              ),

              _buildSettingsTile(
                context,
                title: 'À propos',
                subtitle: 'Infos application',
                icon: MdiIcons.informationOutline,
                onTap: () => _navigateToAbout(context),
              ),

              _buildSettingsTile(
                context,
                title: 'Aide',
                subtitle: 'Guide et FAQ',
                icon: MdiIcons.helpCircleOutline,
                onTap: () => _showNotImplementedDialog(context, 'Aide'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary, size: 18),
        const SizedBox(width: 8),
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return ModernFadeIn(
      delay: const Duration(milliseconds: 100),
      child: ModernCard(
        enableHoverEffect: true,
        onTap: onTap,
        child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: (iconColor ?? Theme.of(context).colorScheme.primary)
                .withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: iconColor ?? Theme.of(context).colorScheme.primary,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontSize: 11,
          ),
        ),
        trailing: const Icon(Icons.chevron_right),
      ),
      ),
    );
  }

  void _navigateToVehicleConfig(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const VehicleConfigScreen()),
    );
  }

  void _navigateToVehicleManagement(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const VehicleManagementScreen()),
    );
  }

  void _navigateToMaintenanceIntervals(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MaintenanceIntervalsScreen(),
      ),
    );
  }

  // Import/Export
  void _showImportExport(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.import_export,
                  color: Colors.green,
                  size: 18,
                ),
              ),
              const SizedBox(width: 10),
              const Text(
                'Import/Export',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.save_alt, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Export: Créer sauvegarde (.dat)',
                      style: TextStyle(fontSize: 13),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.folder_open, color: Colors.blue, size: 16),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Import: Restaurer sauvegarde',
                      style: TextStyle(fontSize: 13),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: const Text(
                      'Annuler',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _handleImport(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.folder_open, size: 14),
                        const SizedBox(width: 4),
                        const Text('Import', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _handleExport(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.save_alt, size: 14),
                        const SizedBox(width: 4),
                        const Text('Export', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  // Gérer l'export
  Future<void> _handleExport(BuildContext context) async {
    Navigator.of(context).pop(); // Fermer le dialog

    // Afficher un indicateur de chargement
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(20)),
        ),
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Export en cours...'),
          ],
        ),
      ),
    );

    try {
      // Fermer le dialog avant d'ouvrir la fenêtre de partage
      if (context.mounted) {
        Navigator.of(context).pop(); // Fermer l'indicateur de chargement
      }

      final success = await ImportExportService.exportData(context);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(success ? 'Export réussi!' : 'Erreur lors de l\'export'),
              ],
            ),
            backgroundColor: success ? Colors.green : Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(
          context,
        ).pop(); // Fermer l'indicateur de chargement en cas d'erreur

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text('Erreur: $e'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  // Gérer l'import
  Future<void> _handleImport(BuildContext context) async {
    Navigator.of(context).pop(); // Fermer le dialog

    // Lancer l'import avec recréation de base et redirection
    debugPrint('🔍 Starting import...');
    try {
      final result = await ImportExportService.importData();
      debugPrint('🔍 Import result: ${result.success}');
      debugPrint('🔍 Import needsReload: ${result.needsReload}');
      debugPrint('🔍 Context mounted: ${context.mounted}');
      debugPrint('🔍 About to check context.mounted...');

      if (result.success) {
        debugPrint('🔍 Import réussi, rechargement des données...');

        // Les données sont importées, l'utilisateur peut naviguer pour les voir
        debugPrint('✅ Import terminé, les données sont dans la base');

        if (context.mounted) {
          // Afficher le message de succès
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ ${result.message}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );

          // Rediriger vers la page d'accueil si on n'y est pas déjà
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      } else {
        if (context.mounted) {
          // Afficher seulement les erreurs
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ Erreur d\'import: ${result.message}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (error, stackTrace) {
      debugPrint('🔍 Import error: $error');
      debugPrint('🔍 Stack trace: $stackTrace');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur: $error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  void _navigateToAbout(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AboutScreen()),
    );
  }

  void _navigateToThemeSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ThemeSettingsScreen()),
    );
  }

  void _navigateToCurrencySettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CurrencySettingsScreen()),
    );
  }

  void _showNotImplementedDialog(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(feature, style: const TextStyle(fontSize: 16)),
        content: const Text(
          'Fonctionnalité en développement.',
          style: TextStyle(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
