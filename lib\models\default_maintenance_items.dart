import 'maintenance_item.dart';

class DefaultMaintenanceItems {
  static List<MaintenanceItem> getDefaultItems() {
    print('🔧 CRÉATION DES ÉLÉMENTS PAR DÉFAUT - NOUVELLES VALEURS');
    final items = [
      // 1. Vidange huile (priorité 1)
      MaintenanceItem(
        name: 'Vidange Huile Moteur',
        description: 'Changement huile moteur et filtre',
        iconPath: 'assets/icons/oil_change.png',
        defaultIntervalKm: 10000,
        currentKm: 0,
        lastMaintenanceKm: 0,
        category: 'Moteur',
        isActive: true, // ⭐ AJOUT CRUCIAL
      ),
      // 2. Filtre gasoil (priorité 2)
      MaintenanceItem(
        name: 'Filtre à Gasoil',
        description: 'Remplacement filtre carburant',
        iconPath: 'assets/icons/fuel_filter.png',
        defaultIntervalKm: 20000,
        currentKm: 0,
        lastMaintenanceKm: 0,
        category: 'Filtres',
        isActive: true, // ⭐ AJOUT CRUCIAL
      ),
      // 3. Filtre à air (priorité 3)
      MaintenanceItem(
        name: 'Filtre à Air',
        description: 'Remplacement filtre air moteur',
        iconPath: 'assets/icons/air_filter.png',
        defaultIntervalKm: 20000,
        currentKm: 0,
        lastMaintenanceKm: 0,
        category: 'Filtres',
        isActive: true, // ⭐ AJOUT CRUCIAL
      ),
      // 4. Filtre habitacle (priorité 4)
      MaintenanceItem(
        name: 'Filtre Habitacle',
        description: 'Remplacement filtre climatisation',
        iconPath: 'assets/icons/cabin_filter.png',
        defaultIntervalKm: 20000,
        currentKm: 0,
        lastMaintenanceKm: 0,
        category: 'Confort',
        isActive: true, // ⭐ AJOUT CRUCIAL
      ),
      // 5. Courroie de distribution (priorité 5)
      MaintenanceItem(
        name: 'Courroie de Distribution',
        description: 'Vérification et remplacement courroie de distribution',
        iconPath: 'assets/icons/belt.png',
        defaultIntervalKm: 60000,
        currentKm: 0,
        lastMaintenanceKm: 0,
        category: 'Moteur',
        isActive: true, // ⭐ AJOUT CRUCIAL
      ),
      // 6. Liquide de refroidissement (priorité 6)
      MaintenanceItem(
        name: 'Liquide de Refroidissement',
        description: 'Vidange système refroidissement',
        iconPath: 'assets/icons/coolant.png',
        defaultIntervalKm: 60000,
        currentKm: 0,
        lastMaintenanceKm: 0,
        category: 'Refroidissement',
        isActive: true, // ⭐ AJOUT CRUCIAL
      ),
      // 7. Liquide de frein (priorité 7)
      MaintenanceItem(
        name: 'Liquide de Frein',
        description: 'Vidange liquide frein',
        iconPath: 'assets/icons/brake_fluid.png',
        defaultIntervalKm: 60000,
        currentKm: 0,
        lastMaintenanceKm: 0,
        category: 'Freinage',
        isActive: true, // ⭐ AJOUT CRUCIAL
      ),
      // 8. Plaquettes de frein (priorité 8)
      MaintenanceItem(
        name: 'Plaquettes de Frein',
        description: 'Vérification et remplacement plaquettes',
        iconPath: 'assets/icons/brake_pads.png',
        defaultIntervalKm: 40000,
        currentKm: 0,
        lastMaintenanceKm: 0,
        category: 'Freinage',
        isActive: true, // ⭐ AJOUT CRUCIAL
      ),
      // 9. Pneus (priorité 9)
      MaintenanceItem(
        name: 'Pneus',
        description: 'Vérification usure et rotation',
        iconPath: 'assets/icons/tires.png',
        defaultIntervalKm: 60000,
        currentKm: 0,
        lastMaintenanceKm: 0,
        category: 'Pneumatiques',
        isActive: true, // ⭐ AJOUT CRUCIAL
      ),
    ];

    // Log des valeurs pour débogage
    for (final item in items) {
      print('📋 ${item.name}: ${item.defaultIntervalKm} km');
    }

    return items;
  }

  static Map<String, List<String>> getMaintenanceCategories() {
    return {
      'Moteur': [
        'Vidange Huile Moteur',
        'Courroie de Distribution',
      ],
      'Filtres': ['Filtre à Air', 'Filtre à Gasoil'],
      'Freinage': ['Plaquettes de Frein', 'Liquide de Frein'],
      'Pneumatiques': ['Pneus'],
      'Refroidissement': ['Liquide de Refroidissement'],
      'Confort': ['Filtre Habitacle'],
    };
  }
}
