# Carosti - Flutter Car Maintenance App

## Commands
- **Build**: `flutter build apk`, `flutter build web`, `flutter build windows`
- **Test**: `flutter test` (run all tests), `flutter test test/widget_test.dart` (run single test)
- **Lint**: `flutter analyze`
- **Format**: `dart format .`
- **Clean**: `flutter clean && flutter pub get`

## Architecture
- **App**: Flutter mobile/web/desktop car maintenance tracking app
- **Database**: SQLite with `sqflite` for local storage
- **State**: Provider pattern for state management
- **Structure**: `/lib/models/` (data), `/lib/providers/` (state), `/lib/services/` (business logic), `/lib/screens/` (UI), `/lib/widgets/` (components)
- **Key Services**: database_service, backup_service, import_export_service, admob_service

## Code Style
- **Language**: Dart with Flutter 3.32.4+
- **Lints**: Uses `flutter_lints` package with standard rules
- **Imports**: Local imports use relative paths, package imports absolute
- **Naming**: snake_case for files/variables, PascalCase for classes
- **Async**: Use async/await, handle errors with try-catch
- **Comments**: French comments for business logic, English for technical code
