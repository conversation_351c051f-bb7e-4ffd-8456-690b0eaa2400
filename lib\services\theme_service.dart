import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeService extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  static const String _colorSchemeKey = 'color_scheme';

  ThemeMode _themeMode = ThemeMode.system;
  String _colorScheme = 'corporate_blue';

  ThemeMode get themeMode => _themeMode;
  String get colorScheme => _colorScheme;

  bool get isLightMode => true; // Toujours mode clair

  // Système de couleurs Material Design 3 moderne
  static const Map<String, Color> modernColors = {
    'corporate_blue': Color(0xFF1E40AF),
    'professional_green': Color(0xFF059669),
    'vibrant_orange': Color(0xFFEA580C),
    'modern_purple': Color(0xFF7C3AED), // Violet moderne
    'tech_cyan': Color(0xFF0891B2), // Cyan technologique
  };

  // Palette de couleurs neutres professionnelles
  static const professionalNeutrals = {
    'white': Color(0xFFFFFFFF),
    'gray_50': Color(0xFFF9FAFB),
    'gray_100': Color(0xFFF3F4F6),
    'gray_200': Color(0xFFE5E7EB),
    'gray_300': Color(0xFFD1D5DB),
    'gray_400': Color(0xFF9CA3AF),
    'gray_500': Color(0xFF6B7280),
    'gray_600': Color(0xFF4B5563),
    'gray_700': Color(0xFF374151),
    'gray_800': Color(0xFF1F2937),
    'gray_900': Color(0xFF111827),
  };

  // Couleurs sémantiques professionnelles
  static const professionalSemantics = {
    'success': Color(0xFF10B981),
    'warning': Color(0xFFF59E0B),
    'error': Color(0xFFEF4444),
    'info': Color(0xFF3B82F6),
  };

  /// Initialise le service de thème
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();

    // Charger le mode de thème
    final themeModeString = prefs.getString(_themeKey) ?? 'system';
    _themeMode = _parseThemeMode(themeModeString);

    // Charger le schéma de couleurs
    _colorScheme = prefs.getString(_colorSchemeKey) ?? 'blue';

    notifyListeners();
  }

  /// Change le mode de thème
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    notifyListeners();

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_themeKey, _themeModeToString(mode));
  }

  /// Change le schéma de couleurs
  Future<void> setColorScheme(String scheme) async {
    if (_colorScheme == scheme) return;

    _colorScheme = scheme;
    notifyListeners();

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_colorSchemeKey, scheme);
  }

  /// Bascule entre mode clair et sombre
  Future<void> toggleTheme() async {
    final newMode = _themeMode == ThemeMode.light
        ? ThemeMode.dark
        : ThemeMode.light;
    await setThemeMode(newMode);
  }

  /// Obtient le thème clair moderne Material 3
  ThemeData getLightTheme() {
    final primaryColor =
        modernColors[_colorScheme] ??
        modernColors['corporate_blue']!;

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
        surface: professionalNeutrals['white']!,
      ),
      scaffoldBackgroundColor: professionalNeutrals['gray_50']!,
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.white,
          letterSpacing: 0,
        ),
        surfaceTintColor: Colors.transparent,
      ),
      datePickerTheme: DatePickerThemeData(
        backgroundColor: professionalNeutrals['white']!,
        headerBackgroundColor: primaryColor,
        headerForegroundColor: Colors.white,
        dayForegroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.white;
          }
          return professionalNeutrals['gray_900']!;
        }),
        dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return Colors.transparent;
        }),
        todayForegroundColor: WidgetStateProperty.all(primaryColor),
        todayBackgroundColor: WidgetStateProperty.all(Colors.transparent),
        yearForegroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.white;
          }
          return professionalNeutrals['gray_900']!;
        }),
        yearBackgroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return Colors.transparent;
        }),
        headerHeadlineStyle: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
        headerHelpStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.white70,
        ),
        weekdayStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: professionalNeutrals['gray_600']!,
        ),
        dayStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        yearStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 8,
        shadowColor: professionalNeutrals['gray_900']!.withValues(alpha: 0.1),
      ),

      cardTheme: CardThemeData(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: professionalNeutrals['white']!,
        shadowColor: professionalNeutrals['gray_900']!.withValues(alpha: 0.04),
        surfaceTintColor: Colors.transparent,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            letterSpacing: 0.5,
          ),
        ),
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: const Color(0xFFF7F8FC),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        hintStyle: TextStyle(
          color: Colors.grey[500],
          fontWeight: FontWeight.w400,
        ),
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.w700,
          color: Color(0xFF1A202C),
          letterSpacing: -0.5,
        ),
        headlineMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w600,
          color: Color(0xFF2D3748),
          letterSpacing: -0.25,
        ),
        titleLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: Color(0xFF2D3748),
          letterSpacing: 0,
        ),
        titleMedium: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: Color(0xFF4A5568),
          letterSpacing: 0.15,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: Color(0xFF4A5568),
          letterSpacing: 0.5,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: Color(0xFF718096),
          letterSpacing: 0.25,
        ),
      ),
    );
  }

  /// Convertit ThemeMode en String
  String _themeModeToString(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  /// Parse String vers ThemeMode
  ThemeMode _parseThemeMode(String mode) {
    switch (mode) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
        return ThemeMode.system;
      default:
        return ThemeMode.system;
    }
  }

  /// Obtient le nom d'affichage du mode de thème
  String getThemeModeDisplayName(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'Clair';
      case ThemeMode.dark:
        return 'Sombre';
      case ThemeMode.system:
        return 'Système';
    }
  }

  /// Obtient l'icône du mode de thème
  IconData getThemeModeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  /// Obtient le nom d'affichage du schéma de couleurs
  String getColorSchemeDisplayName(String scheme) {
    switch (scheme) {
      case 'corporate_blue':
        return 'Bleu Corporate';
      case 'business_gray':
        return 'Gris Business';
      case 'professional_green':
        return 'Vert Professionnel';
      case 'executive_purple':
        return 'Violet Exécutif';
      case 'enterprise_teal':
        return 'Sarcelle Entreprise';
      case 'minimal_dark':
        return 'Sombre Minimal';
      default:
        return 'Bleu Corporate';
    }
  }

  /// Vérifie si une couleur est sombre
  bool isColorDark(Color color) {
    return color.computeLuminance() < 0.5;
  }

  /// Obtient une couleur contrastante
  Color getContrastColor(Color color) {
    return isColorDark(color) ? Colors.white : Colors.black;
  }

  /// Obtient la couleur primaire actuelle
  Color get primaryColor =>
      modernColors[_colorScheme] ?? modernColors['corporate_blue']!;

  /// Obtient toutes les couleurs disponibles
  List<MapEntry<String, Color>> get availableColorEntries =>
      modernColors.entries.toList();

  /// Compatibilité avec l'ancien code
  static Map<String, Color> get professionalColors => modernColors;

  /// Crée un fond professionnel subtil
  BoxDecoration createProfessionalBackground({bool isDark = false}) {
    return BoxDecoration(
      color: isDark
          ? professionalNeutrals['gray_900']!
          : professionalNeutrals['gray_50']!,
    );
  }

  /// Crée une ombre professionnelle
  List<BoxShadow> createProfessionalShadow({bool elevated = false}) {
    return [
      BoxShadow(
        color: professionalNeutrals['gray_900']!.withValues(
          alpha: elevated ? 0.08 : 0.04,
        ),
        blurRadius: elevated ? 16 : 8,
        offset: Offset(0, elevated ? 8 : 4),
      ),
    ];
  }

  /// Réinitialise aux paramètres par défaut
  Future<void> resetToDefaults() async {
    await setThemeMode(ThemeMode.system);
    await setColorScheme('corporate_blue');
  }

  /// Obtient les statistiques d'utilisation du thème
  Map<String, dynamic> getThemeStats() {
    return {
      'currentMode': 'Clair',
      'currentColor': getColorSchemeDisplayName(_colorScheme),
      'isLightMode': isLightMode,
      'primaryColor': primaryColor.toString(),
    };
  }
}
