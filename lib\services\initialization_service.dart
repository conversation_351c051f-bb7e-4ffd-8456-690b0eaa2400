import '../models/vehicle_config.dart';
import '../models/default_maintenance_items.dart';
import '../models/maintenance_history.dart';
import 'database_service.dart';

class InitializationService {
  static final InitializationService _instance =
      InitializationService._internal();
  factory InitializationService() => _instance;
  InitializationService._internal();

  final DatabaseService _databaseService = DatabaseService();

  Future<void> initializeApp() async {
    try {
      // Vérifier s'il y a déjà un véhicule par défaut
      final existingVehicle = await _databaseService.getDefaultVehicleConfig();

      if (existingVehicle == null) {
        // Créer un véhicule par défaut
        await _createDefaultVehicle();
      }

      // Vérifier et mettre à jour les éléments d'entretien si nécessaire
      await _ensureMaintenanceItemsExist();

      // Ne plus créer d'historiques d'exemple par défaut
      // await _createSampleHistoryIfNeeded();
    } catch (e) {
      print('Erreur lors de l\'initialisation: $e');
      rethrow;
    }
  }

  Future<void> _createDefaultVehicle() async {
    final defaultVehicle = VehicleConfig(
      vehicleName: 'Mon Véhicule',
      brand: 'Marque',
      model: 'Modèle',
      year: DateTime.now().year,
      currentKilometers: 0,
      lastKmUpdate: DateTime.now(),
      isDefault: true,
    );

    await _databaseService.insertVehicleConfig(defaultVehicle);
  }

  Future<void> _ensureMaintenanceItemsExist() async {
    final existingItems = await _databaseService.getAllMaintenanceItems();

    if (existingItems.isEmpty) {
      // Insérer les éléments par défaut
      final defaultItems = DefaultMaintenanceItems.getDefaultItems();
      for (final item in defaultItems) {
        await _databaseService.insertMaintenanceItem(item);
      }
    }
  }

  Future<void> _createSampleHistoryIfNeeded() async {
    final existingHistory = await _databaseService.getAllMaintenanceHistory();

    if (existingHistory.isEmpty) {
      // Créer quelques entrées d'historique d'exemple avec des coûts réalistes
      final now = DateTime.now();
      final sampleHistory = [
        // Entretiens de ce mois
        MaintenanceHistory(
          maintenanceItemId: 1, // Vidange huile moteur
          maintenanceDate: now.subtract(const Duration(days: 15)),
          kilometersAtMaintenance: 19500,
          cost: 85.0,
          location: 'Garage Central',
          mechanicName: 'Jean Dupont',
          notes: 'Vidange complète avec filtre à huile',
        ),
        MaintenanceHistory(
          maintenanceItemId: 3, // Filtre à air
          maintenanceDate: now.subtract(const Duration(days: 10)),
          kilometersAtMaintenance: 19800,
          cost: 30.0,
          location: 'Garage Central',
          mechanicName: 'Jean Dupont',
          notes: 'Remplacement filtre à air',
        ),

        // Entretiens des mois précédents (cette année)
        MaintenanceHistory(
          maintenanceItemId: 6, // Plaquettes de frein
          maintenanceDate: now.subtract(const Duration(days: 45)),
          kilometersAtMaintenance: 18500,
          cost: 160.0,
          location: 'Garage Central',
          mechanicName: 'Marie Martin',
          notes: 'Remplacement plaquettes avant',
        ),
        MaintenanceHistory(
          maintenanceItemId: 5, // Filtre habitacle
          maintenanceDate: now.subtract(const Duration(days: 75)),
          kilometersAtMaintenance: 17800,
          cost: 35.0,
          location: 'Garage Express',
          mechanicName: 'Pierre Durand',
          notes: 'Remplacement filtre habitacle',
        ),
        MaintenanceHistory(
          maintenanceItemId: 7, // Liquide de frein
          maintenanceDate: now.subtract(const Duration(days: 120)),
          kilometersAtMaintenance: 16500,
          cost: 50.0,
          location: 'Garage Central',
          mechanicName: 'Jean Dupont',
          notes: 'Purge et remplacement liquide de frein',
        ),

        // Entretien plus coûteux
        MaintenanceHistory(
          maintenanceItemId: 8, // Pneus
          maintenanceDate: now.subtract(const Duration(days: 180)),
          kilometersAtMaintenance: 15000,
          cost: 420.0,
          location: 'Centre Pneus',
          mechanicName: 'Équipe technique',
          notes: 'Remplacement 4 pneus été',
        ),
      ];

      for (final history in sampleHistory) {
        await _databaseService.insertMaintenanceHistory(history);
      }
    }
  }

  Future<void> resetToDefaults() async {
    await _databaseService.resetDatabase();
    await initializeApp();
  }
}
