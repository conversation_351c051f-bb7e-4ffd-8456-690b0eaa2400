@echo off
echo ========================================
echo    CAROSTI - Installation Propre
echo ========================================
echo.

echo 1. Désinstallation de l'ancienne version...
flutter install --uninstall-only
timeout /t 2 /nobreak >nul

echo.
echo 2. Nettoyage des caches...
flutter clean
timeout /t 1 /nobreak >nul

echo.
echo 3. Récupération des dépendances...
flutter pub get
timeout /t 2 /nobreak >nul

echo.
echo 4. Construction de l'APK signé...
flutter build apk --release

echo.
echo 5. Installation de la nouvelle version...
flutter install

echo.
echo ========================================
echo    Installation terminée !
echo ========================================
echo.
echo APK disponible dans : build\app\outputs\flutter-apk\app-release.apk
echo.
pause
