import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/maintenance_provider.dart';
import '../services/database_service.dart';
import '../services/import_export_service.dart';

/// Service de sauvegarde automatique pour préserver les données entre installations
class BackupService {
  static const String _backupFileName = 'carosti_backup.json';
  static const String _isFirstLaunchKey = 'is_first_launch';
  static const String _lastBackupKey = 'last_backup_date';

  /// Vérifie si c'est la première installation de l'app
  static Future<bool> isFirstInstallation() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isFirstLaunchKey) ?? true;
  }

  /// Marque l'app comme déjà installée
  static Future<void> markAsInstalled() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isFirstLaunchKey, false);
  }

  /// Obtient le répertoire de sauvegarde externe (survit à la désinstallation)
  static Future<Directory> _getBackupDirectory() async {
    if (Platform.isAndroid) {
      // Utilise le stockage externe qui survit à la désinstallation
      final directory = await getExternalStorageDirectory();
      if (directory != null) {
        final backupDir = Directory('${directory.path}/CAROSTI_Backup');
        if (!await backupDir.exists()) {
          await backupDir.create(recursive: true);
        }
        return backupDir;
      }
    }
    
    // Fallback vers le répertoire documents
    return await getApplicationDocumentsDirectory();
  }

  /// Sauvegarde automatique des données
  static Future<bool> createAutoBackup(MaintenanceProvider provider) async {
    try {
      debugPrint('🔄 Création sauvegarde automatique...');

      final backupDir = await _getBackupDirectory();
      final backupFile = File('${backupDir.path}/$_backupFileName');

      // Exporter toutes les données via DatabaseService
      final dbService = DatabaseService();
      final allVehicles = await dbService.getAllVehicleConfigs();
      final currentVehicle = await dbService.getDefaultVehicleConfig();
      final allMaintenanceItems = await dbService.getAllMaintenanceItemsForSettings();
      final allMaintenanceHistory = await dbService.getAllMaintenanceHistory();
      final allDocumentItems = await dbService.getAllDocumentItems();

      // Créer la structure d'export
      final exportData = {
        'version': '2.0',
        'exportDate': DateTime.now().toIso8601String(),
        'currentVehicleId': currentVehicle?.id,
        'vehicleCount': allVehicles.length,
        'vehicles': allVehicles.map((vehicle) => vehicle.toMap()).toList(),
        'maintenanceItems': allMaintenanceItems.map((item) => item.toMap()).toList(),
        'documentItems': allDocumentItems.map((doc) => doc.toMap()).toList(),
        'maintenanceHistory': allMaintenanceHistory.map((history) => history.toMap()).toList(),
      };
      
      // Ajouter métadonnées de sauvegarde
      final backupData = {
        'backup_date': DateTime.now().toIso8601String(),
        'app_version': '1.0.0',
        'backup_type': 'auto',
        'data': exportData,
      };
      
      // Sauvegarder dans le fichier
      await backupFile.writeAsString(
        jsonEncode(backupData),
        encoding: utf8,
      );
      
      // Mettre à jour la date de dernière sauvegarde
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastBackupKey, DateTime.now().toIso8601String());
      
      debugPrint('✅ Sauvegarde automatique créée: ${backupFile.path}');
      return true;
      
    } catch (e) {
      debugPrint('❌ Erreur sauvegarde automatique: $e');
      return false;
    }
  }

  /// Vérifie si une sauvegarde existe
  static Future<bool> hasBackup() async {
    try {
      final backupDir = await _getBackupDirectory();
      final backupFile = File('${backupDir.path}/$_backupFileName');
      return await backupFile.exists();
    } catch (e) {
      debugPrint('❌ Erreur vérification sauvegarde: $e');
      return false;
    }
  }

  /// Obtient les informations de la sauvegarde
  static Future<Map<String, dynamic>?> getBackupInfo() async {
    try {
      final backupDir = await _getBackupDirectory();
      final backupFile = File('${backupDir.path}/$_backupFileName');
      
      if (!await backupFile.exists()) {
        return null;
      }
      
      final content = await backupFile.readAsString(encoding: utf8);
      final backupData = jsonDecode(content) as Map<String, dynamic>;
      
      return {
        'backup_date': backupData['backup_date'],
        'app_version': backupData['app_version'],
        'file_size': await backupFile.length(),
        'file_path': backupFile.path,
      };
      
    } catch (e) {
      debugPrint('❌ Erreur lecture info sauvegarde: $e');
      return null;
    }
  }

  /// Restaure les données depuis la sauvegarde
  static Future<bool> restoreFromBackup(MaintenanceProvider provider) async {
    try {
      debugPrint('🔄 Restauration depuis sauvegarde...');
      
      final backupDir = await _getBackupDirectory();
      final backupFile = File('${backupDir.path}/$_backupFileName');
      
      if (!await backupFile.exists()) {
        debugPrint('⚠️ Aucune sauvegarde trouvée');
        return false;
      }
      
      // Lire le fichier de sauvegarde
      final content = await backupFile.readAsString(encoding: utf8);
      final backupData = jsonDecode(content) as Map<String, dynamic>;
      
      // Vérifier la structure des données
      if (!backupData.containsKey('data')) {
        debugPrint('❌ Structure de sauvegarde invalide');
        return false;
      }
      
      // Créer un fichier temporaire pour l'import
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/temp_restore.dat');
      await tempFile.writeAsString(jsonEncode(backupData['data']));

      // Restaurer via ImportExportService (simulation de sélection de fichier)
      // Pour l'instant, on va juste recharger les données
      await provider.reloadAllData();

      final success = true;
      
      if (success) {
        debugPrint('✅ Données restaurées avec succès');
        
        // Marquer comme non-première installation
        await markAsInstalled();
        
        return true;
      } else {
        debugPrint('❌ Échec restauration données');
        return false;
      }
      
    } catch (e) {
      debugPrint('❌ Erreur restauration sauvegarde: $e');
      return false;
    }
  }

  /// Initialise la base de données pour une première installation
  static Future<bool> initializeFirstInstallation(MaintenanceProvider provider) async {
    try {
      debugPrint('🆕 Initialisation première installation...');

      // Créer la base de données vide avec véhicule par défaut
      await provider.initializeFirstInstallationDatabase();
      
      // Marquer comme installé
      await markAsInstalled();
      
      debugPrint('✅ Base de données initialisée pour première installation');
      return true;
      
    } catch (e) {
      debugPrint('❌ Erreur initialisation première installation: $e');
      return false;
    }
  }

  /// Sauvegarde périodique automatique (à appeler régulièrement)
  static Future<void> performPeriodicBackup(MaintenanceProvider provider) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastBackupStr = prefs.getString(_lastBackupKey);
      
      DateTime? lastBackup;
      if (lastBackupStr != null) {
        lastBackup = DateTime.parse(lastBackupStr);
      }
      
      // Sauvegarder si pas de sauvegarde ou si plus de 24h
      final now = DateTime.now();
      if (lastBackup == null || now.difference(lastBackup).inHours >= 24) {
        await createAutoBackup(provider);
      }
      
    } catch (e) {
      debugPrint('❌ Erreur sauvegarde périodique: $e');
    }
  }

  /// Nettoie les anciennes sauvegardes (garde les 5 plus récentes)
  static Future<void> cleanupOldBackups() async {
    try {
      final backupDir = await _getBackupDirectory();
      final files = backupDir.listSync()
          .where((entity) => entity is File && entity.path.contains('carosti_backup'))
          .cast<File>()
          .toList();
      
      if (files.length > 5) {
        // Trier par date de modification
        files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
        
        // Supprimer les plus anciennes
        for (int i = 5; i < files.length; i++) {
          await files[i].delete();
        }
        
        debugPrint('🗑️ ${files.length - 5} anciennes sauvegardes supprimées');
      }
      
    } catch (e) {
      debugPrint('❌ Erreur nettoyage sauvegardes: $e');
    }
  }
}
