class MaintenanceItem {
  final int? id;
  final String name;
  final String description;
  final String iconPath;
  final int defaultIntervalKm;
  final int? customIntervalKm;
  final int currentKm;
  final int lastMaintenanceKm;
  final DateTime? lastMaintenanceDate;
  final bool isActive;
  final String category;
  final int? vehicleId;

  MaintenanceItem({
    this.id,
    required this.name,
    required this.description,
    required this.iconPath,
    required this.defaultIntervalKm,
    this.customIntervalKm,
    required this.currentKm,
    required this.lastMaintenanceKm,
    this.lastMaintenanceDate,
    this.isActive = true,
    required this.category,
    this.vehicleId,
  });

  // Calculer le kilométrage restant avant le prochain entretien
  int get kmUntilNextMaintenance {
    final intervalKm = customIntervalKm ?? defaultIntervalKm;
    final nextMaintenanceKm = lastMaintenanceKm + intervalKm;
    return nextMaintenanceKm - currentKm;
  }

  // Calculer le pourcentage d'usure
  double get usagePercentage {
    final intervalKm = customIntervalKm ?? defaultIntervalKm;
    final kmSinceLastMaintenance = currentKm - lastMaintenanceKm;
    return (kmSinceLastMaintenance / intervalKm).clamp(0.0, 1.0);
  }

  // Vérifier si l'entretien est dû
  bool get isMaintenanceDue {
    return kmUntilNextMaintenance <= 0;
  }

  // Vérifier si l'entretien approche (dans les 1000 km)
  bool get isMaintenanceApproaching {
    return kmUntilNextMaintenance <= 1000 && kmUntilNextMaintenance > 0;
  }

  // Marquer l'entretien comme effectué à un kilométrage spécifique
  MaintenanceItem markAsCompleted({
    required int atKilometers,
    DateTime? completionDate,
  }) {
    return copyWith(
      lastMaintenanceKm: atKilometers,
      lastMaintenanceDate: completionDate ?? DateTime.now(),
      // Ne pas modifier currentKm - il reste indépendant
    );
  }

  // Convertir vers Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconPath': iconPath,
      'defaultIntervalKm': defaultIntervalKm,
      'customIntervalKm': customIntervalKm,
      'currentKm': currentKm,
      'lastMaintenanceKm': lastMaintenanceKm,
      'lastMaintenanceDate': lastMaintenanceDate?.millisecondsSinceEpoch,
      'isActive': isActive ? 1 : 0,
      'category': category,
      'vehicle_id': vehicleId,
    };
  }

  // Créer depuis Map (base de données)
  factory MaintenanceItem.fromMap(Map<String, dynamic> map) {
    return MaintenanceItem(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      iconPath: map['iconPath'],
      defaultIntervalKm: map['defaultIntervalKm'],
      customIntervalKm: map['customIntervalKm'],
      currentKm: map['currentKm'],
      lastMaintenanceKm: map['lastMaintenanceKm'],
      lastMaintenanceDate: map['lastMaintenanceDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastMaintenanceDate'])
          : null,
      isActive: map['isActive'] == 1,
      category: map['category'],
      vehicleId: map['vehicle_id'],
    );
  }

  // Copier avec modifications
  MaintenanceItem copyWith({
    int? id,
    String? name,
    String? description,
    String? iconPath,
    int? defaultIntervalKm,
    Object? customIntervalKm = _undefined,
    int? currentKm,
    int? lastMaintenanceKm,
    DateTime? lastMaintenanceDate,
    bool? isActive,
    String? category,
    int? vehicleId,
  }) {
    return MaintenanceItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconPath: iconPath ?? this.iconPath,
      defaultIntervalKm: defaultIntervalKm ?? this.defaultIntervalKm,
      customIntervalKm: customIntervalKm == _undefined
          ? this.customIntervalKm
          : customIntervalKm as int?,
      currentKm: currentKm ?? this.currentKm,
      lastMaintenanceKm: lastMaintenanceKm ?? this.lastMaintenanceKm,
      lastMaintenanceDate: lastMaintenanceDate ?? this.lastMaintenanceDate,
      isActive: isActive ?? this.isActive,
      category: category ?? this.category,
      vehicleId: vehicleId ?? this.vehicleId,
    );
  }

  @override
  String toString() {
    return 'MaintenanceItem{id: $id, name: $name, category: $category}';
  }
}

// Objet sentinelle pour distinguer null d'une valeur non fournie
const Object _undefined = Object();
