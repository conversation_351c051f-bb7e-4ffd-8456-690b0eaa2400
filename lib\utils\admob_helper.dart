import 'package:flutter/material.dart';
import '../services/admob_service.dart';

/// Helper pour gérer les publicités interstitielles
class AdMobHelper {
  static final AdMobService _adMobService = AdMobService();

  /// Affiche une publicité interstitielle avant de naviguer vers une page
  static void showInterstitialAndNavigate(
    BuildContext context,
    Widget destination, {
    bool forceShow = false,
  }) {
    // Vérifier si on doit afficher la publicité
    if (forceShow || _shouldShowInterstitial()) {
      _adMobService.showInterstitialAd();
      
      // Attendre un peu puis naviguer
      Future.delayed(const Duration(milliseconds: 500), () {
        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => destination),
          );
        }
      });
    } else {
      // Navigation directe
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => destination),
      );
    }
  }

  /// Affiche une publicité interstitielle après une action importante
  static void showInterstitialAfterAction({
    VoidCallback? onComplete,
    bool forceShow = false,
  }) {
    if (forceShow || _shouldShowInterstitial()) {
      _adMobService.showInterstitialAd();
      
      // Callback après un délai
      if (onComplete != null) {
        Future.delayed(const Duration(milliseconds: 1000), onComplete);
      }
    } else {
      // Callback immédiat
      onComplete?.call();
    }
  }

  /// Logique pour déterminer quand afficher les interstitielles
  static bool _shouldShowInterstitial() {
    // Pour l'instant, afficher 1 fois sur 3 (33% du temps)
    // Vous pouvez ajuster cette logique selon vos besoins
    return DateTime.now().millisecondsSinceEpoch % 3 == 0;
  }

  /// Affiche une interstitielle avec un délai personnalisé
  static void showInterstitialWithDelay({
    Duration delay = const Duration(milliseconds: 500),
    VoidCallback? onComplete,
  }) {
    Future.delayed(delay, () {
      if (_shouldShowInterstitial()) {
        _adMobService.showInterstitialAd();
      }
      onComplete?.call();
    });
  }
}

/// Extension pour faciliter l'utilisation des interstitielles
extension AdMobNavigation on BuildContext {
  /// Navigation avec publicité interstitielle
  void pushWithAd(Widget destination) {
    AdMobHelper.showInterstitialAndNavigate(this, destination);
  }

  /// Navigation de remplacement avec publicité
  void pushReplacementWithAd(Widget destination) {
    if (AdMobHelper._shouldShowInterstitial()) {
      AdMobHelper._adMobService.showInterstitialAd();
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          Navigator.pushReplacement(
            this,
            MaterialPageRoute(builder: (context) => destination),
          );
        }
      });
    } else {
      Navigator.pushReplacement(
        this,
        MaterialPageRoute(builder: (context) => destination),
      );
    }
  }
}
