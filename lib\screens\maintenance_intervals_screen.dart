import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../models/maintenance_item.dart';
import '../models/document_item.dart';
import '../models/default_maintenance_items.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_animations.dart';

class MaintenanceIntervalsScreen extends StatefulWidget {
  const MaintenanceIntervalsScreen({super.key});

  @override
  State<MaintenanceIntervalsScreen> createState() =>
      _MaintenanceIntervalsScreenState();
}

class _MaintenanceIntervalsScreenState
    extends State<MaintenanceIntervalsScreen> {
  bool _isLoading = false;
  List<MaintenanceItem> _allItems = []; // Inclut actifs et inactifs

  @override
  void initState() {
    super.initState();
    _loadAllItems();
  }

  Future<void> _loadAllItems() async {
    final provider = context.read<MaintenanceProvider>();
    final items = await provider.getAllMaintenanceItemsForSettings();
    setState(() {
      _allItems = items;
    });
  }

  // Activer/Désactiver un entretien
  Future<void> _toggleItemActive(
    BuildContext context,
    MaintenanceItem item,
  ) async {
    final provider = context.read<MaintenanceProvider>();
    await provider.toggleMaintenanceItemActive(item);
    await _loadAllItems(); // Recharger la liste
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Intervalles',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.restore, size: 20),
            onPressed: _showResetToDefaultDialog,
            tooltip: 'Restaurer défaut',
          ),
        ],
      ),
      body: Consumer<MaintenanceProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // En-tête informatif
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue[700]),
                          const SizedBox(width: 8),
                          Text(
                            'Config intervalles',
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue[700],
                                  fontSize: 12,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Personnalisez selon vos besoins. Valeurs par défaut constructeurs.',
                        style: TextStyle(color: Colors.blue[700], fontSize: 10),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Liste des catégories et éléments (tous les entretiens)
                ..._buildAllCategorySections(context, _allItems),
              ],
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildAllCategorySections(
    BuildContext context,
    List<MaintenanceItem> items,
  ) {
    // Grouper tous les entretiens par catégorie
    final Map<String, List<MaintenanceItem>> itemsByCategory = {};
    for (final item in items) {
      // Masquer la catégorie "Autre" sur la page des intervalles
      if (item.category.toLowerCase() == 'autre') {
        continue;
      }

      if (!itemsByCategory.containsKey(item.category)) {
        itemsByCategory[item.category] = [];
      }
      itemsByCategory[item.category]!.add(item);
    }

    return itemsByCategory.entries
        .map((entry) => _buildCategorySection(context, entry.key, entry.value))
        .toList();
  }





  Widget _buildCategorySection(
    BuildContext context,
    String category,
    List<MaintenanceItem> items,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // En-tête de catégorie
        Row(
          children: [
            Icon(
              _getIconForCategory(category),
              color: Theme.of(context).colorScheme.primary,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              category,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.primary,
                fontSize: 14,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Cartes des éléments
        ...items.map(
          (item) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildMaintenanceItemCard(context, item),
          ),
        ),

        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildMaintenanceItemCard(BuildContext context, MaintenanceItem item) {
    final hasCustomInterval = item.customIntervalKm != null;
    final currentInterval = item.customIntervalKm ?? item.defaultIntervalKm;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec nom, icône et switch ON/OFF
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getIconForCategory(item.category),
                    color: item.isActive
                        ? Theme.of(context).colorScheme.primary
                        : Colors.grey,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.name,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                          color: item.isActive ? null : Colors.grey,
                        ),
                      ),
                      Text(
                        item.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: item.isActive
                              ? Colors.grey[600]
                              : Colors.grey[400],
                          fontSize: 10,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                // Switch ON/OFF minimisé (toujours accessible)
                Column(
                  children: [
                    Transform.scale(
                      scale: 0.6, // Plus petit
                      child: Switch(
                        value: item.isActive,
                        onChanged: (value) => _toggleItemActive(context, item),
                        activeColor: Theme.of(context).colorScheme.primary,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                    Text(
                      item.isActive ? 'ON' : 'OFF',
                      style: TextStyle(
                        fontSize: 7,
                        color: item.isActive
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    // Badge Custom juste en dessous du switch (seulement si actif ET custom)
                    if (item.isActive && hasCustomInterval)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Custom',
                          style: TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.w600,
                            color: Colors.orange[700],
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),

            // Afficher les détails seulement si actif
            if (item.isActive) ...[
              const SizedBox(height: 16),

              // Informations sur les intervalles
              Row(
                children: [
                  Expanded(
                    child: _buildIntervalInfo(
                      context,
                      'Défaut',
                      '${item.defaultIntervalKm} km',
                      Icons.settings,
                      Colors.grey[600]!,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildIntervalInfo(
                      context,
                      'Actuel',
                      '${currentInterval} km',
                      hasCustomInterval ? Icons.edit : Icons.check,
                      hasCustomInterval ? Colors.orange : Colors.green,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Boutons d'action
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _showEditIntervalDialog(context, item),
                      icon: const Icon(Icons.edit, size: 14),
                      label: const Text(
                        'Modifier',
                        style: TextStyle(fontSize: 10),
                      ),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 6),
                        minimumSize: const Size(0, 28),
                      ),
                    ),
                  ),
                  if (hasCustomInterval) ...[
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _resetToDefault(context, item),
                        icon: const Icon(Icons.restore, size: 16),
                        label: const Text(
                          'Défaut',
                          style: TextStyle(fontSize: 12),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[600],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          minimumSize: const Size(0, 32),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildIntervalInfo(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 10,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: color,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  void _showEditIntervalDialog(BuildContext context, MaintenanceItem item) {
    final controller = TextEditingController(
      text: (item.customIntervalKm ?? item.defaultIntervalKm).toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Modifier intervalle',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.name,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Défaut: ${item.defaultIntervalKm} km',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontSize: 11,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: controller,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                labelText: 'Intervalle',
                suffixText: 'km',
                border: const OutlineInputBorder(),
                hintText: '15000',
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler', style: TextStyle(fontSize: 11)),
          ),
          ElevatedButton(
            onPressed: () async {
              final newInterval = int.tryParse(controller.text);
              if (newInterval != null && newInterval > 0) {
                Navigator.pop(context);
                await _updateInterval(context, item, newInterval);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Veuillez entrer un intervalle valide'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text('Sauvegarder', style: TextStyle(fontSize: 11)),
          ),
        ],
      ),
    );
  }

  Future<void> _updateInterval(
    BuildContext context,
    MaintenanceItem item,
    int newInterval,
  ) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await context.read<MaintenanceProvider>().updateMaintenanceInterval(
        item,
        newInterval,
      );

      // Recharger les données pour mise à jour immédiate
      await _loadAllItems();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Intervalle mis à jour'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _resetToDefault(
    BuildContext context,
    MaintenanceItem item,
  ) async {
    // Afficher le popup de confirmation moderne
    final bool? confirmed = await _showResetConfirmationDialog(context, item);

    if (confirmed != true) return; // Annulé par l'utilisateur

    setState(() {
      _isLoading = true;
    });

    try {
      await context.read<MaintenanceProvider>().resetToDefaultInterval(item);

      // Recharger les données pour mise à jour immédiate
      await _loadAllItems();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                const Text('Intervalle restauré'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text('Erreur: $e'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<bool?> _showResetConfirmationDialog(
    BuildContext context,
    MaintenanceItem item,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.restore, color: Colors.orange[700], size: 24),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Restaurer par défaut',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Voulez-vous restaurer l\'intervalle par défaut pour :',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getIconForCategory(item.category),
                      color: Colors.orange[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 13,
                            ),
                          ),
                          Text(
                            'Actuel: ${item.customIntervalKm} km → Défaut: ${item.defaultIntervalKm} km',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Cette action ne peut pas être annulée.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              child: Text(
                'Annuler',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                  fontSize: 11,
                ),
              ),
            ),
            ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(true),
              icon: const Icon(Icons.restore, size: 16),
              label: const Text(
                'Restaurer',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 11),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showResetToDefaultDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Restaurer les valeurs par défaut',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        content: const Text(
          'Êtes-vous sûr de vouloir restaurer tous les intervalles '
          'aux valeurs par défaut ? Tous vos réglages personnalisés seront perdus.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler', style: TextStyle(fontSize: 11)),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              // TODO: Implement reset all to default
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Fonctionnalité en développement'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text('Restaurer', style: TextStyle(fontSize: 11)),
          ),
        ],
      ),
    );
  }

  IconData _getIconForCategory(String category) {
    switch (category.toLowerCase()) {
      case 'moteur':
        return MdiIcons.engine;
      case 'filtres':
        return MdiIcons.airFilter;
      case 'freinage':
        return MdiIcons.carBrakeAlert;
      case 'pneumatiques':
        return MdiIcons.carTireAlert;
      case 'refroidissement':
        return MdiIcons.radiator;
      case 'confort':
        return MdiIcons.airConditioner;
      default:
        return MdiIcons.wrench;
    }
  }
}
