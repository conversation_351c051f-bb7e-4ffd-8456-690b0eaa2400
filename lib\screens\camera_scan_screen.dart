import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

class CameraScanScreen extends StatefulWidget {
  final Function(int) onKilometersDetected;

  const CameraScanScreen({
    super.key,
    required this.onKilometersDetected,
  });

  @override
  State<CameraScanScreen> createState() => _CameraScanScreenState();
}

class _CameraScanScreenState extends State<CameraScanScreen> {
  final _kilometerController = TextEditingController();
  bool _isScanning = false;

  @override
  void dispose() {
    _kilometerController.dispose();
    super.dispose();
  }

  Future<void> _simulateScan() async {
    setState(() {
      _isScanning = true;
    });

    // Simuler un délai de scan
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isScanning = false;
    });

    // Afficher un dialogue pour saisir manuellement le kilométrage
    _showManualInputDialog();
  }

  void _showManualInputDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Saisir le kilométrage'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'La fonctionnalité de scan automatique sera disponible dans une prochaine version. '
              'Veuillez saisir le kilométrage manuellement.',
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _kilometerController,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: const InputDecoration(
                labelText: 'Kilométrage',
                suffixText: 'km',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              final km = int.tryParse(_kilometerController.text);
              if (km != null && km > 0) {
                Navigator.pop(context);
                widget.onKilometersDetected(km);
                Navigator.pop(context);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Veuillez entrer un kilométrage valide'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Confirmer'),
          ),
        ],
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scanner le kilométrage'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              MdiIcons.cameraOff,
              size: 100,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'Scan automatique temporairement indisponible',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'La fonctionnalité de scan automatique du tableau de bord '
              'sera disponible dans une prochaine version de l\'application.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isScanning ? null : _simulateScan,
                icon: _isScanning
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Icon(MdiIcons.keyboard),
                label: Text(_isScanning ? 'Traitement...' : 'Saisie manuelle'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back),
                label: const Text('Retour'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
