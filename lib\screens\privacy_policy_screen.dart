import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Politique de confidentialité',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.white,
            fontSize: 16,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête avec icône
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.blue.withValues(alpha: 0.1),
                      Colors.blue.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.privacy_tip_outlined,
                        color: Colors.blue,
                        size: 32,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Votre vie privée est importante',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Carosti respecte et protège vos données personnelles.\n'
                      'Découvrez comment nous garantissons votre confidentialité.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Sections de la politique
              _buildSection(
                context,
                'Stockage des données',
                'Toutes vos données de maintenance sont stockées localement sur votre appareil. '
                    'Aucune information n\'est transmise à des serveurs externes ou à des tiers.',
                Icons.storage_rounded,
                Colors.green,
              ),

              _buildSection(
                context,
                'Aucune collecte',
                'Carosti ne collecte aucune donnée personnelle. Vos informations de véhicule, '
                    'historique de maintenance et préférences restent entièrement privées.',
                Icons.block_rounded,
                Colors.orange,
              ),

              _buildSection(
                context,
                'Publicités Google Ads',
                'Cette application utilise Google AdMob pour afficher des publicités. Google peut '
                    'collecter certaines informations anonymes à des fins publicitaires, notamment :\n\n'
                    '• Identifiant publicitaire de l\'appareil\n'
                    '• Informations sur l\'appareil (modèle, système d\'exploitation)\n'
                    '• Données de localisation approximative\n'
                    '• Interactions avec les publicités\n\n'
                    'Ces données sont traitées par Google selon leur politique de confidentialité. '
                    'Vous pouvez désactiver la personnalisation des annonces dans les paramètres de votre appareil.',
                Icons.ads_click_rounded,
                Colors.amber,
              ),

              _buildSection(
                context,
                'Partenaires tiers',
                'Les publicités affichées peuvent provenir de partenaires publicitaires tiers de Google. '
                    'Ces partenaires peuvent également collecter des informations anonymes pour personnaliser '
                    'les annonces. Aucune donnée personnelle de l\'application (véhicules, entretiens) '
                    'n\'est partagée avec ces partenaires.\n\n'
                    'Pour plus d\'informations :\n'
                    '• Politique Google AdMob : policies.google.com/privacy\n'
                    '• Paramètres publicitaires : adssettings.google.com',
                Icons.business_rounded,
                Colors.deepOrange,
              ),

              _buildSection(
                context,
                'Données locales uniquement',
                'L\'application fonctionne entièrement en mode hors ligne. Vos données ne quittent '
                    'jamais votre appareil, garantissant une confidentialité totale.',
                Icons.phone_android_rounded,
                Colors.purple,
              ),

              _buildSection(
                context,
                'Sécurité',
                'Vos données sont protégées par les mécanismes de sécurité de votre appareil. '
                    'Nous recommandons d\'utiliser un code PIN ou une authentification biométrique.',
                Icons.security_rounded,
                Colors.red,
              ),

              _buildSection(
                context,
                'Sauvegarde',
                'Si vous souhaitez sauvegarder vos données, utilisez les fonctions de sauvegarde '
                    'de votre appareil. Carosti ne propose pas de synchronisation cloud.',
                Icons.backup_rounded,
                Colors.teal,
              ),

              _buildSection(
                context,
                'Contrôle des publicités',
                'Vous avez le contrôle sur les publicités affichées :\n\n'
                    '• Les publicités se masquent automatiquement sans connexion internet\n'
                    '• Vous pouvez désactiver la personnalisation dans les paramètres Android/iOS\n'
                    '• Paramètres > Confidentialité > Publicité > Désactiver la personnalisation\n'
                    '• Les publicités restent anonymes et ne sont pas liées à vos données de maintenance\n\n'
                    'L\'affichage de publicités nous permet de maintenir l\'application gratuite.',
                Icons.tune_rounded,
                Colors.cyan,
              ),

              _buildSection(
                context,
                'Contact',
                'Pour toute question concernant cette politique de confidentialité, '
                    'vous pouvez nous contacter à l\'adresse suivante :\n\n'
                    '📧 <EMAIL>\n\n'
                    'Nous répondrons dans les plus brefs délais.',
                Icons.contact_support_rounded,
                Colors.indigo,
              ),

              const SizedBox(height: 32),

              // Footer
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  children: [
                    Text(
                      'Dernière mise à jour',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Janvier 2025 - version 1.1.0',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
