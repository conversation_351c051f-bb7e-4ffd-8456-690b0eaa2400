import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../services/admob_service.dart';

/// Widget bannière spécifique pour la page statistiques
class AdMobStatsBanner extends StatefulWidget {
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;

  const AdMobStatsBanner({
    super.key,
    this.margin,
    this.backgroundColor,
  });

  @override
  State<AdMobStatsBanner> createState() => _AdMobStatsBannerState();
}

class _AdMobStatsBannerState extends State<AdMobStatsBanner> {
  final AdMobService _adMobService = AdMobService();
  late Timer _timer;
  static const String _pageId = 'stats';

  @override
  void initState() {
    super.initState();
    // Recharger la bannière si nécessaire
    _adMobService.reloadBannerIfNeeded(_pageId);
    
    // Timer pour mettre à jour l'état toutes les secondes
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Vérifier si AdMob est supporté sur cette plateforme
    if (!_adMobService.isSupported) {
      return const SizedBox.shrink();
    }

    // Vérifier si AdMob est initialisé
    if (!_adMobService.isInitialized) {
      return _buildLoadingContainer('Initialisation AdMob...');
    }

    // Vérifier si la bannière est prête
    if (!_adMobService.isBannerAdReady(_pageId) || _adMobService.getBannerAd(_pageId) == null) {
      return _buildLoadingContainer('Chargement publicité...');
    }

    // Afficher la bannière
    final bannerAd = _adMobService.getBannerAd(_pageId)!;
    return Container(
      height: bannerAd.size.height.toDouble(),
      margin: widget.margin ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: AdWidget(ad: bannerAd),
      ),
    );
  }

  Widget _buildLoadingContainer(String message) {
    return Container(
      height: 50,
      margin: widget.margin ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              message,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget bannière pour le bas de la page statistiques
class AdMobStatsBottomBanner extends StatelessWidget {
  final Color? backgroundColor;

  const AdMobStatsBottomBanner({
    super.key,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: AdMobStatsBanner(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          backgroundColor: backgroundColor,
        ),
      ),
    );
  }
}
