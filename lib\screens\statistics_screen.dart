import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../models/maintenance_item.dart';
import '../models/maintenance_history.dart';
import '../models/default_maintenance_items.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_animations.dart';
import '../widgets/shimmer_loading.dart';
import '../widgets/smart_admob_banner.dart';
import '../services/currency_service.dart';
import 'currency_settings_screen.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  String _selectedPeriod = '6 mois';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Statistiques',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Icône devise
          Consumer<CurrencyService>(
            builder: (context, currencyService, child) {
              return IconButton(
                icon: Icon(currencyService.getCurrencyIcon(), size: 20),
                tooltip: 'Changer devise (${currencyService.currencyCode})',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CurrencySettingsScreen(),
                    ),
                  );
                },
              );
            },
          ),
          // Menu période
          PopupMenuButton<String>(
            icon: Icon(MdiIcons.clockOutline, size: 20),
            tooltip: 'Période d\'analyse',
            onSelected: (value) {
              setState(() {
                _selectedPeriod = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: '3 mois', child: Text('3 mois')),
              const PopupMenuItem(value: '6 mois', child: Text('6 mois')),
              const PopupMenuItem(value: '1 année', child: Text('1 année')),
              const PopupMenuItem(value: 'Tout', child: Text('Tout')),
            ],
          ),
        ],
      ),
      body: Consumer<MaintenanceProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Coûts d'entretien
                _buildCostAnalysis(provider),

                const SizedBox(height: 16),

                // Historique par entretien
                _buildMaintenanceHistory(provider),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: const SmartAdMobBottomBanner(pageId: 'stats'),
    );
  }

  Widget _buildPeriodHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              MdiIcons.chartLine,
              color: Theme.of(context).colorScheme.primary,
              size: 24,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Période d\'analyse',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _selectedPeriod,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 6),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: color,
                fontSize: 16,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Répartition par catégorie
  Widget _buildCategoryDistribution(MaintenanceProvider provider) {
    final categories = provider.itemsByCategory;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Répartition',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: categories.entries.map((entry) {
                final category = entry.key;
                final items = entry.value;
                final percentage =
                    (items.length / provider.maintenanceItems.length * 100);

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 6),
                  child: Row(
                    children: [
                      Icon(
                        _getIconForCategory(category),
                        size: 16,
                        color: _getColorForCategory(category),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          category,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Text(
                        '${items.length}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        '${percentage.toInt()}%',
                        style: TextStyle(color: Colors.grey[600], fontSize: 10),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCostAnalysis(MaintenanceProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Coûts d\'Entretien',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
        ),
        const SizedBox(height: 16),

        ModernFadeIn(
          delay: const Duration(milliseconds: 100),
          child: ModernCard(
            padding: const EdgeInsets.all(16),
            enableHoverEffect: true,
            child: Column(
              children: [
                // Coûts réels seulement (0€ si pas d'historique)
                () {
                  // Vérifier s'il y a un historique avec des coûts
                  if (provider.maintenanceHistory.isEmpty) {
                    return Row(
                      children: [
                        Expanded(
                          child: _buildCostCard(
                            'Ce mois',
                            _formatCost(0),
                            MdiIcons.calendar,
                            Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildCostCard(
                            'Cette année',
                            _formatCost(0),
                            MdiIcons.calendarRange,
                            Colors.green,
                          ),
                        ),
                      ],
                    );
                  }

                  // Calculer les coûts réels directement ici
                  final now = DateTime.now();

                  // Coût mensuel - seulement historiques avec coûts réels DU VÉHICULE ACTUEL
                  final startOfMonth = DateTime(now.year, now.month, 1);
                  final endOfMonth = DateTime(now.year, now.month + 1, 0);
                  final currentVehicleId = provider.currentVehicle?.id;

                  final monthlyCost = currentVehicleId != null
                      ? provider.maintenanceHistory
                          .where((h) {
                            // Vérifier que l'historique appartient au véhicule actuel
                            final item = provider.maintenanceItems
                                .where((item) => item.id == h.maintenanceItemId)
                                .firstOrNull;

                            return item?.vehicleId == currentVehicleId &&
                                (h.maintenanceDate.isAfter(startOfMonth) ||
                                 h.maintenanceDate.isAtSameMomentAs(startOfMonth)) &&
                                (h.maintenanceDate.isBefore(endOfMonth) ||
                                 h.maintenanceDate.isAtSameMomentAs(endOfMonth)) &&
                                h.cost != null &&
                                h.cost! > 0;
                          })
                          .fold(0.0, (sum, h) => sum + h.cost!)
                      : 0.0;

                  // Coût selon la période sélectionnée
                  final periodCost = _calculateCostForPeriod(
                    provider,
                    _selectedPeriod,
                  );

                  return Row(
                    children: [
                      Expanded(
                        child: _buildCostCard(
                          'Ce mois',
                          monthlyCost > 0 ? _formatCost(monthlyCost) : _formatCost(0),
                          MdiIcons.calendar,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildCostCard(
                          _selectedPeriod,
                          periodCost > 0 ? _formatCost(periodCost) : _formatCost(0),
                          MdiIcons.calendarRange,
                          Colors.green,
                        ),
                      ),
                    ],
                  );
                }(),

                const SizedBox(height: 16),

                // Répartition par entretien
                _buildMaintenanceCostDistribution(provider),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Répartition des coûts par entretien POUR LE VÉHICULE ACTUEL
  Widget _buildMaintenanceCostDistribution(MaintenanceProvider provider) {
    // Calculer les coûts par entretien depuis l'historique DU VÉHICULE ACTUEL
    final costsByMaintenance = <String, double>{};
    final currentVehicleId = provider.currentVehicle?.id;

    if (currentVehicleId == null) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          children: [
            Icon(
              MdiIcons.informationOutline,
              color: Colors.grey[600],
              size: 14,
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                'Aucun véhicule sélectionné',
                style: TextStyle(color: Colors.grey[600], fontSize: 10),
              ),
            ),
          ],
        ),
      );
    }

    // Parcourir l'historique des entretiens pour récupérer les coûts DU VÉHICULE ACTUEL
    for (final history in provider.maintenanceHistory) {
      if (history.cost != null && history.cost! > 0) {
        // Trouver l'entretien correspondant
        try {
          final item = provider.maintenanceItems.firstWhere(
            (item) => item.id == history.maintenanceItemId,
          );

          // VÉRIFIER QUE L'ENTRETIEN APPARTIENT AU VÉHICULE ACTUEL
          if (item.vehicleId == currentVehicleId) {
            // Additionner les coûts si plusieurs entretiens du même type
            final key = (item.category.toLowerCase() == 'autre') ? 'Autre' : _getDisplayName(item.name);
            costsByMaintenance[key] =
                (costsByMaintenance[key] ?? 0.0) + history.cost!;
          }
        } catch (e) {
          // Si l'entretien n'est pas trouvé, ignorer cette entrée
          // Silencieux en production
        }
      }
    }

    // Calculer les coûts par type d'entretien

    if (costsByMaintenance.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          children: [
            Icon(
              MdiIcons.informationOutline,
              color: Colors.grey[600],
              size: 14,
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                'Aucun coût d\'entretien enregistré',
                style: TextStyle(color: Colors.grey[600], fontSize: 10),
              ),
            ),
          ],
        ),
      );
    }

    final totalCost = costsByMaintenance.values.fold(
      0.0,
      (sum, cost) => sum + cost,
    );

    // Ordonner l'affichage: toutes les opérations "Autre" à la fin
    final entries = costsByMaintenance.entries.toList();
    final autresEntries = entries.where((e) => e.key.toLowerCase() == 'autre').toList();
    final nonAutresEntries = entries.where((e) => e.key.toLowerCase() != 'autre').toList();
    final orderedEntries = <MapEntry<String, double>>[
      ...nonAutresEntries,
      ...autresEntries,
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Répartition par Entretien',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 11,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        ...orderedEntries.map((entry) {
          final percentage = (entry.value / totalCost * 100);
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 3),
            child: Row(
              children: [
                Icon(
                  _getIconForMaintenanceItem(entry.key),
                  size: 12,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(entry.key, style: const TextStyle(fontSize: 10)),
                ),
                Text(
                  CurrencyService().formatCost(entry.value),
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '${percentage.toInt()}%',
                  style: TextStyle(color: Colors.grey[500], fontSize: 9),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildCostCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 16,
            ),
          ),
          Text(title, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
        ],
      ),
    );
  }

  // Historique par entretien (même ordre que diagnostic)
  Widget _buildMaintenanceHistory(MaintenanceProvider provider) {
    // Ordre des entretiens selon la page diagnostic
    final maintenanceOrder = [
      'Vidange Huile Moteur',
      'Filtre à Gasoil',
      'Filtre à Air',
      'Filtre Habitacle',
      'Chaîne de Distribution',
      'Courroie d\'Accessoires',
      'Liquide de Refroidissement',
      'Liquide de Frein',
      'Plaquettes de Frein',
      'Pneus',
    ];

    // Filtrer et ordonner les entretiens qui ont un historique
    final itemsWithHistory = provider.maintenanceItems
        .where((item) => item.lastMaintenanceDate != null)
        .toList();

    // Trier selon l'ordre défini
    itemsWithHistory.sort((a, b) {
      final indexA = maintenanceOrder.indexOf(a.name);
      final indexB = maintenanceOrder.indexOf(b.name);

      // Si l'entretien n'est pas dans la liste, le mettre à la fin
      if (indexA == -1 && indexB == -1) return a.name.compareTo(b.name);
      if (indexA == -1) return 1;
      if (indexB == -1) return -1;

      return indexA.compareTo(indexB);
    });

    if (itemsWithHistory.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Historique',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 13,
            ),
          ),
          const SizedBox(height: 12),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(MdiIcons.informationOutline, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Aucun historique d\'entretien disponible.',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Historique',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 13,
              ),
            ),
            GestureDetector(
              onTap: () => _showAddMaintenanceDialog(context, provider),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(Icons.add, size: 14, color: Colors.white),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // Regrouper l'historique par type d'entretien et créer des ExpansionTiles
        ...() {
          // Regrouper l'historique par type d'entretien
          final historyByMaintenance = <String, List<Map<String, dynamic>>>{};
          final customOperations = <Map<String, dynamic>>[];

          for (final history in provider.maintenanceHistory) {
            try {
              final item = provider.maintenanceItems.firstWhere(
                (item) => item.id == history.maintenanceItemId,
              );

              // Séparer les opérations personnalisées (catégorie "Autre")
              if (item.category == 'Autre') {
                customOperations.add({
                  'history': history,
                  'item': item,
                });
              } else {
                if (!historyByMaintenance.containsKey(item.name)) {
                  historyByMaintenance[item.name] = [];
                }

                historyByMaintenance[item.name]!.add({
                  'history': history,
                  'item': item,
                });
              }
            } catch (e) {
              // Ignorer si l'entretien n'est pas trouvé
            }
          }

          // Trier les historiques par date (plus récent en premier)
          historyByMaintenance.forEach((key, value) {
            value.sort(
              (a, b) => (b['history'] as MaintenanceHistory).maintenanceDate
                  .compareTo(
                    (a['history'] as MaintenanceHistory).maintenanceDate,
                  ),
            );
          });

          // Filtrer seulement les entretiens qui ont un historique
          final maintenanceWithHistory = historyByMaintenance.keys.toList();

          // Trier selon l'ordre défini
          final maintenanceOrder = [
            'Vidange Huile Moteur',
            'Filtre à Gasoil',
            'Filtre à Air',
            'Filtre Habitacle',
            'Chaîne de Distribution',
            'Courroie d\'Accessoires',
            'Liquide de Refroidissement',
            'Liquide de Frein',
            'Plaquettes de Frein',
            'Pneus',
          ];

          maintenanceWithHistory.sort((a, b) {
            final indexA = maintenanceOrder.indexOf(a);
            final indexB = maintenanceOrder.indexOf(b);

            if (indexA == -1 && indexB == -1) return a.compareTo(b);
            if (indexA == -1) return 1;
            if (indexB == -1) return -1;

            return indexA.compareTo(indexB);
          });

          return maintenanceWithHistory.map((maintenanceName) {
            final histories = historyByMaintenance[maintenanceName]!;

            return Card(
              margin: const EdgeInsets.only(bottom: 4),
              child: ExpansionTile(
                dense: true,
                visualDensity: VisualDensity.compact,
                tilePadding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 2,
                ),
                childrenPadding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                leading: Icon(
                  _getIconForMaintenanceItem(maintenanceName),
                  size: 14,
                  color: Colors.grey[600],
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        maintenanceName,
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Text(
                      '${histories.length}',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                children: histories.map((historyData) {
                  final history = historyData['history'] as MaintenanceHistory;

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Icon(
                          MdiIcons.clockOutline,
                          size: 10,
                          color: Colors.grey[500],
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            '${history.maintenanceDate.day}/${history.maintenanceDate.month}/${history.maintenanceDate.year}',
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        if (history.cost != null && history.cost! > 0)
                          Text(
                            CurrencyService().formatCost(history.cost!),
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.green[600],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        const SizedBox(width: 6),
                        Text(
                          '${history.kilometersAtMaintenance} km',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 6),
                        GestureDetector(
                          onTap: () => _showEditMaintenanceDialog(
                            context,
                            provider,
                            history,
                          ),
                          child: Icon(
                            Icons.edit,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            );
          }).toList()
          ..addAll([
            // Section "Autre" pour les opérations personnalisées
            if (customOperations.isNotEmpty)
              Card(
                margin: const EdgeInsets.only(bottom: 4),
                child: ExpansionTile(
                  dense: true,
                  visualDensity: VisualDensity.compact,
                  tilePadding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  childrenPadding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  leading: Icon(
                    Icons.build,
                    size: 14,
                    color: Colors.grey[600],
                  ),
                  title: Row(
                    children: [
                      const Expanded(
                        child: Text(
                          'Autre',
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Text(
                        '${customOperations.length}',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  children: customOperations.map((operationData) {
                    final history = operationData['history'] as MaintenanceHistory;
                    final item = operationData['item'] as MaintenanceItem;

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        children: [
                          Icon(
                            Icons.build,
                            size: 10,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${history.maintenanceDate.day}/${history.maintenanceDate.month}/${history.maintenanceDate.year}',
                                  style: const TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  _getDisplayName(item.name),
                                  style: TextStyle(
                                    fontSize: 9,
                                    color: Colors.grey[600],
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (history.cost != null && history.cost! > 0)
                            Text(
                              CurrencyService().formatCost(history.cost!),
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.green[600],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          const SizedBox(width: 6),
                          Text(
                            '${history.kilometersAtMaintenance} km',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 6),
                          GestureDetector(
                            onTap: () => _showEditMaintenanceDialog(
                              context,
                              provider,
                              history,
                            ),
                            child: Icon(
                              Icons.edit,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
          ]);
        }(),
      ],
    );
  }

  Widget _buildPredictions(MaintenanceProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Prédictions',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      MdiIcons.crystalBall,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Prochains Entretiens',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                ...provider.maintenanceItems
                    .where(
                      (item) => provider.calculateWearPercentage(item) > 50,
                    )
                    .take(5)
                    .map((item) {
                      final wearPercentage = provider.calculateWearPercentage(
                        item,
                      );
                      final estimatedDate = provider
                          .calculateEstimatedMaintenanceDate(item);

                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            Icon(
                              _getIconForMaintenanceItem(item.name),
                              size: 20,
                              color: _getWearColor(wearPercentage),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  if (estimatedDate != null)
                                    Text(
                                      _formatEstimatedDate(estimatedDate),
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: _getWearColor(
                                  wearPercentage,
                                ).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: _getWearColor(wearPercentage),
                                ),
                              ),
                              child: Text(
                                '${wearPercentage.toInt()}%',
                                style: TextStyle(
                                  color: _getWearColor(wearPercentage),
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),

                if (provider.maintenanceItems
                    .where(
                      (item) => provider.calculateWearPercentage(item) > 50,
                    )
                    .isEmpty)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(MdiIcons.checkCircle, color: Colors.green[700]),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Aucun entretien urgent prévu dans les prochains mois !',
                            style: TextStyle(
                              color: Colors.green[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Méthodes utilitaires
  IconData _getIconForCategory(String category) {
    switch (category.toLowerCase()) {
      case 'moteur':
        return MdiIcons.engine;
      case 'filtres':
        return MdiIcons.airFilter;
      case 'freinage':
        return MdiIcons.carBrakeAlert;
      case 'pneumatiques':
        return MdiIcons.carTireAlert;
      case 'refroidissement':
        return MdiIcons.radiator;
      case 'confort':
        return MdiIcons.airConditioner;
      default:
        return MdiIcons.wrench;
    }
  }

  Color _getColorForCategory(String category) {
    switch (category.toLowerCase()) {
      case 'moteur':
        return Colors.red;
      case 'filtres':
        return Colors.blue;
      case 'freinage':
        return Colors.orange;
      case 'pneumatiques':
        return Colors.purple;
      case 'refroidissement':
        return Colors.cyan;
      case 'confort':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Color _getWearColor(double wearPercentage) {
    if (wearPercentage >= 100) {
      return Colors.red;
    } else if (wearPercentage >= 80) {
      return Colors.orange;
    } else if (wearPercentage >= 50) {
      return Colors.yellow[700]!;
    } else {
      return Colors.green;
    }
  }

  IconData _getIconForMaintenanceItem(String maintenanceName) {
    switch (maintenanceName.toLowerCase()) {
      case 'vidange huile moteur':
        return MdiIcons.oilLevel;
      case 'chaîne de distribution':
        return MdiIcons.linkVariant;
      case 'filtre à air':
        return MdiIcons.airFilter;
      case 'filtre à gasoil':
        return MdiIcons.gasStation;
      case 'filtre habitacle':
        return MdiIcons.airConditioner;
      case 'plaquettes de frein':
        return MdiIcons.carBrakeAlert;
      case 'liquide de frein':
        return MdiIcons.carBrakeRetarder;
      case 'pneus':
        return MdiIcons.carTireAlert;
      case 'liquide de refroidissement':
        return MdiIcons.radiator;
      case 'courroie d\'accessoires':
        return MdiIcons.carCruiseControl;
      default:
        return MdiIcons.wrench;
    }
  }

  String _formatEstimatedDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference <= 0) {
      return 'Maintenant';
    } else if (difference == 1) {
      return 'Demain';
    } else if (difference < 7) {
      return 'Dans $difference jours';
    } else if (difference < 30) {
      final weeks = (difference / 7).round();
      return 'Dans $weeks semaine${weeks > 1 ? 's' : ''}';
    } else {
      final months = (difference / 30).round();
      return 'Dans $months mois';
    }
  }

  String _formatCost(double cost) {
    final currencyService = CurrencyService();
    return currencyService.formatCostCompact(cost);
  }

  // Méthode de formatage de date (période relative)
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return ""; // Supprimer "Aujourd'hui"
    } else if (difference == 1) {
      return "Hier";
    } else if (difference < 7) {
      return "Il y a $difference jour${difference > 1 ? 's' : ''}";
    } else if (difference < 30) {
      final weeks = (difference / 7).round();
      return "Il y a $weeks semaine${weeks > 1 ? 's' : ''}";
    } else if (difference < 365) {
      final months = (difference / 30).round();
      return "Il y a $months mois";
    } else {
      final years = (difference / 365).round();
      return "Il y a $years an${years > 1 ? 's' : ''}";
    }
  }

  // Calculer le coût selon la période sélectionnée POUR LE VÉHICULE ACTUEL
  double _calculateCostForPeriod(MaintenanceProvider provider, String period) {
    final now = DateTime.now();
    DateTime startDate;

    switch (period) {
      case '3 mois':
        startDate = DateTime(now.year, now.month - 2, 1);
        break;
      case '6 mois':
        startDate = DateTime(now.year, now.month - 5, 1);
        break;
      case '1 année':
        startDate = DateTime(now.year, 1, 1);
        break;
      case 'Tout':
        startDate = DateTime(
          2000,
          1,
          1,
        ); // Date très ancienne pour inclure tout
        break;
      default:
        startDate = DateTime(now.year, 1, 1); // Par défaut 1 année
    }

    // FILTRER PAR VÉHICULE ACTUEL
    final currentVehicleId = provider.currentVehicle?.id;
    if (currentVehicleId == null) return 0.0;

    return provider.maintenanceHistory
        .where((h) {
          // Vérifier que l'historique appartient au véhicule actuel
          final item = provider.maintenanceItems
              .where((item) => item.id == h.maintenanceItemId)
              .firstOrNull;

          return item?.vehicleId == currentVehicleId &&
              (h.maintenanceDate.isAfter(startDate) ||
               h.maintenanceDate.isAtSameMomentAs(startDate)) &&
              (h.maintenanceDate.isBefore(now) ||
               h.maintenanceDate.isAtSameMomentAs(now)) &&
              h.cost != null &&
              h.cost! > 0;
        })
        .fold(0.0, (sum, h) => sum + h.cost!);
  }

  // Dialog pour ajouter un entretien à l'historique
  void _showAddMaintenanceDialog(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    String? selectedMaintenanceId;
    final costController = TextEditingController();
    final kmController = TextEditingController();
    final customOperationController = TextEditingController();
    DateTime selectedDate = DateTime.now();
    bool isCustomOperation = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.add_circle_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Ajouter Entretien',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                // Liste déroulante des entretiens
                DropdownButtonFormField<String>(
                  value: selectedMaintenanceId,
                  decoration: const InputDecoration(
                    labelText: 'Type d\'entretien',
                    labelStyle: TextStyle(fontSize: 12),
                  ),
                  style: const TextStyle(fontSize: 12, color: Colors.black),
                  items: [
                    // Entretiens existants (triés selon l'ordre des intervalles)
                    ...() {
                      // Filtrer les entretiens
                      final filteredItems = provider.maintenanceItems
                          .where((item) =>
                              item.vehicleId == provider.currentVehicle?.id &&
                              item.isActive &&
                              !item.name.startsWith('[CUSTOM]')) // Exclure les entretiens personnalisés temporaires
                          .toList();

                      // Trier selon l'ordre défini dans DefaultMaintenanceItems
                      final defaultItems = DefaultMaintenanceItems.getDefaultItems();
                      final orderMap = <String, int>{};
                      for (int i = 0; i < defaultItems.length; i++) {
                        orderMap[defaultItems[i].name] = i;
                      }

                      filteredItems.sort((a, b) {
                        final orderA = orderMap[a.name] ?? 999;
                        final orderB = orderMap[b.name] ?? 999;
                        return orderA.compareTo(orderB);
                      });

                      return filteredItems.map((item) {
                        return DropdownMenuItem(
                          value: item.id.toString(),
                          child: Text(
                            item.name.split(' (').first,
                            style: const TextStyle(fontSize: 12),
                          ),
                        );
                      });
                    }(),
                    // Option "Autre"
                    const DropdownMenuItem(
                      value: 'autre',
                      child: Text(
                        'Autre',
                        style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedMaintenanceId = value;
                      isCustomOperation = value == 'autre';
                    });
                  },
                ),
                const SizedBox(height: 12),
                // Champ pour opération personnalisée (affiché seulement si "Autre" est sélectionné)
                if (isCustomOperation) ...[
                  TextFormField(
                    controller: customOperationController,
                    decoration: const InputDecoration(
                      labelText: 'Opération',
                      labelStyle: TextStyle(fontSize: 12),
                      hintText: 'Saisir l\'opération',
                      hintStyle: TextStyle(fontSize: 11),
                    ),
                    style: const TextStyle(fontSize: 12),
                  ),
                  const SizedBox(height: 12),
                ],
                // Champ coût
                TextFormField(
                  controller: costController,
                  decoration: InputDecoration(
                    labelText: 'Coût (${CurrencyService().currencySymbol})',
                    labelStyle: const TextStyle(fontSize: 12),
                  ),
                  style: const TextStyle(fontSize: 12),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 12),
                // Champ kilométrage
                TextFormField(
                  controller: kmController,
                  decoration: const InputDecoration(
                    labelText: 'Kilométrage',
                    labelStyle: TextStyle(fontSize: 12),
                  ),
                  style: const TextStyle(fontSize: 12),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 12),
                // Sélecteur de date
                Row(
                  children: [
                    const Text('Date: ', style: TextStyle(fontSize: 12)),
                    TextButton(
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: selectedDate,
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                          locale: const Locale('fr', 'FR'), // Forcer français
                        );
                        if (date != null) {
                          setState(() {
                            selectedDate = date;
                          });
                        }
                      },
                      child: Text(
                        '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            ),
          ),
          actions: [
            SizedBox(
              width: double.maxFinite,
              child: ElevatedButton.icon(
                onPressed: () async {
                  if (selectedMaintenanceId != null &&
                      costController.text.isNotEmpty &&
                      kmController.text.isNotEmpty) {

                    if (isCustomOperation && customOperationController.text.isNotEmpty) {
                      // Créer une opération personnalisée (historique seulement)
                      await provider.addCustomMaintenanceHistoryOnly(
                        customOperationController.text,
                        double.parse(costController.text),
                        int.parse(kmController.text),
                        selectedDate,
                      );
                      setState(() {}); // Rafraîchir l'affichage
                    } else if (!isCustomOperation) {
                      // Utiliser un entretien existant
                      await _addMaintenanceToHistory(
                        provider,
                        int.parse(selectedMaintenanceId!),
                        double.parse(costController.text),
                        int.parse(kmController.text),
                        selectedDate,
                      );
                    }
                    if (context.mounted) Navigator.pop(context);
                  }
                },
                icon: const Icon(Icons.add, size: 16),
                label: const Text(
                  'Ajouter à l\'historique',
                  style: TextStyle(fontSize: 12),
                ),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Dialog pour modifier un entretien
  void _showEditMaintenanceDialog(
    BuildContext context,
    MaintenanceProvider provider,
    MaintenanceHistory history,
  ) {
    final costController = TextEditingController(
      text: history.cost?.toString() ?? '',
    );
    final kmController = TextEditingController(
      text: history.kilometersAtMaintenance.toString(),
    );
    DateTime selectedDate = history.maintenanceDate;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text(
            'Modifier Entretien',
            style: TextStyle(fontSize: 14),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Champ coût
              TextFormField(
                controller: costController,
                decoration: InputDecoration(
                  labelText: 'Coût (${CurrencyService().currencySymbol})',
                  labelStyle: const TextStyle(fontSize: 12),
                ),
                style: const TextStyle(fontSize: 12),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 12),
              // Champ kilométrage
              TextFormField(
                controller: kmController,
                decoration: const InputDecoration(
                  labelText: 'Kilométrage',
                  labelStyle: TextStyle(fontSize: 12),
                ),
                style: const TextStyle(fontSize: 12),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 12),
              // Sélecteur de date
              Row(
                children: [
                  const Text('Date: ', style: TextStyle(fontSize: 12)),
                  TextButton(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: selectedDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                        locale: const Locale('fr', 'FR'), // Forcer français
                      );
                      if (date != null) {
                        setState(() {
                          selectedDate = date;
                        });
                      }
                    },
                    child: Text(
                      '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () =>
                        _confirmDelete(context, provider, history.id!),
                    style: TextButton.styleFrom(
                      backgroundColor: Colors.red.withValues(alpha: 0.1),
                      foregroundColor: Colors.red,
                    ),
                    child: const Text(
                      'Supprimer',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      if (costController.text.isNotEmpty &&
                          kmController.text.isNotEmpty) {
                        await _updateMaintenanceInHistory(
                          provider,
                          history.id!,
                          double.parse(costController.text),
                          int.parse(kmController.text),
                          selectedDate,
                        );
                        if (context.mounted) Navigator.pop(context);
                      }
                    },
                    child: const Text(
                      'Modifier',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Fonction utilitaire pour nettoyer le nom d'affichage
  String _getDisplayName(String name) {
    return name.startsWith('[CUSTOM]') ? name.substring(9) : name;
  }

  // Méthodes pour gérer l'historique

  Future<void> _addMaintenanceToHistory(
    MaintenanceProvider provider,
    int maintenanceItemId,
    double cost,
    int kilometers,
    DateTime date,
  ) async {
    // VÉRIFICATION : S'assurer que l'entretien existe et récupérer son véhicule
    final maintenanceItem = provider.maintenanceItems
        .where((item) => item.id == maintenanceItemId)
        .firstOrNull;

    if (maintenanceItem == null) {
      debugPrint('❌ ERREUR: Entretien ID $maintenanceItemId non trouvé !');
      // Afficher un message d'erreur à l'utilisateur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur: Entretien non trouvé'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    debugPrint('✅ Ajout historique: ${maintenanceItem.name} pour véhicule ${maintenanceItem.vehicleId}');

    final history = MaintenanceHistory(
      maintenanceItemId: maintenanceItemId,
      kilometersAtMaintenance: kilometers,
      maintenanceDate: date,
      cost: cost,
    );

    await provider.addMaintenanceHistory(history);
    setState(() {}); // Rafraîchir l'affichage
  }

  Future<void> _updateMaintenanceInHistory(
    MaintenanceProvider provider,
    int historyId,
    double cost,
    int kilometers,
    DateTime date,
  ) async {
    // Récupérer l'historique existant
    final existingHistory = provider.maintenanceHistory.firstWhere(
      (h) => h.id == historyId,
    );

    // Créer une version mise à jour
    final updatedHistory = existingHistory.copyWith(
      cost: cost,
      kilometersAtMaintenance: kilometers,
      maintenanceDate: date,
    );

    // Utiliser la nouvelle méthode du provider
    await provider.updateMaintenanceHistory(updatedHistory);
    setState(() {}); // Rafraîchir l'affichage
  }

  // Confirmation moderne pour suppression
  void _confirmDelete(
    BuildContext context,
    MaintenanceProvider provider,
    int historyId,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.warning_amber_rounded,
                color: Colors.red,
                size: 18,
              ),
            ),
            const SizedBox(width: 10),
            const Text(
              'Confirmer suppression',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        content: const Text(
          'Supprimer cette opération définitivement ?',
          style: TextStyle(fontSize: 13),
        ),
        actions: [
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () => Navigator.pop(context),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                  child: const Text('Annuler', style: TextStyle(fontSize: 12)),
                ),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: ElevatedButton(
                  onPressed: () async {
                    Navigator.pop(context); // Fermer confirmation
                    Navigator.pop(context); // Fermer dialog modification
                    await provider.deleteMaintenanceHistory(historyId);
                    setState(() {}); // Rafraîchir l'affichage
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.delete, size: 14),
                      const SizedBox(width: 4),
                      const Text('Supprimer', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
