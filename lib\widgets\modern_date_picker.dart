import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

class ModernDatePicker {
  /// Affiche un date picker moderne avec des boutons personnalisés
  static Future<DateTime?> show({
    required BuildContext context,
    required DateTime initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
    String? helpText,
    String? cancelText,
    String? confirmText,
  }) async {
    return await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime.now(),
      helpText: helpText ?? 'Sélectionner une date',
      cancelText: cancelText ?? 'Annuler',
      confirmText: confirmText ?? 'Valider',
      locale: const Locale('fr', 'FR'), // Forcer la localisation française
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            dialogTheme: DialogThemeData(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
          child: child!,
        );
      },
    );
  }

  /// Affiche un date range picker moderne
  static Future<DateTimeRange?> showRange({
    required BuildContext context,
    DateTimeRange? initialDateRange,
    DateTime? firstDate,
    DateTime? lastDate,
    String? helpText,
    String? cancelText,
    String? confirmText,
  }) async {
    return await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime.now(),
      helpText: helpText ?? 'Sélectionner une période',
      cancelText: cancelText ?? 'Annuler',
      confirmText: confirmText ?? 'Valider',
      locale: const Locale('fr', 'FR'), // Forcer la localisation française
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            dialogTheme: DialogThemeData(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
          child: child!,
        );
      },
    );
  }
}

/// Widget pour afficher une date avec possibilité de modification
class ModernDateSelector extends StatelessWidget {
  final DateTime selectedDate;
  final VoidCallback onTap;
  final String? label;
  final String? subtitle;
  final IconData? icon;
  final bool enabled;

  const ModernDateSelector({
    super.key,
    required this.selectedDate,
    required this.onTap,
    this.label,
    this.subtitle,
    this.icon,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        enabled: enabled,
        leading: Icon(
          icon ?? MdiIcons.calendar,
          color: enabled ? Theme.of(context).colorScheme.primary : Colors.grey,
          size: 20,
        ),
        title: Text(
          label ?? _formatDate(selectedDate),
          style: TextStyle(fontSize: 13, color: enabled ? null : Colors.grey),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle!,
                style: TextStyle(
                  fontSize: 11,
                  color: enabled ? Colors.grey[600] : Colors.grey,
                ),
              )
            : null,
        trailing: enabled
            ? Icon(Icons.chevron_right, color: Colors.grey[400], size: 20)
            : null,
        onTap: enabled ? onTap : null,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

/// Widget compact pour afficher une date
class CompactDateDisplay extends StatelessWidget {
  final DateTime date;
  final VoidCallback? onTap;
  final bool showIcon;
  final Color? color;

  const CompactDateDisplay({
    super.key,
    required this.date,
    this.onTap,
    this.showIcon = true,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              color?.withValues(alpha: 0.1) ??
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color ?? Theme.of(context).colorScheme.primary,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showIcon) ...[
              Icon(
                MdiIcons.calendar,
                size: 16,
                color: color ?? Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 6),
            ],
            Text(
              _formatDate(date),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color ?? Theme.of(context).colorScheme.primary,
              ),
            ),
            if (onTap != null) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.edit,
                size: 14,
                color: color ?? Theme.of(context).colorScheme.primary,
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
