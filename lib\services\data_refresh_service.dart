import 'dart:async';

/// Service pour notifier les changements de données après import
class DataRefreshService {
  static final DataRefreshService _instance = DataRefreshService._internal();
  factory DataRefreshService() => _instance;
  DataRefreshService._internal();

  final StreamController<bool> _refreshController = StreamController<bool>.broadcast();
  
  /// Stream pour écouter les demandes de rechargement
  Stream<bool> get refreshStream => _refreshController.stream;
  
  /// Notifier qu'un rechargement est nécessaire
  void notifyDataChanged() {
    _refreshController.add(true);
  }
  
  void dispose() {
    _refreshController.close();
  }
}
