// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:mycar_maintenance/models/maintenance_item.dart';
import 'package:mycar_maintenance/models/vehicle_config.dart';

void main() {
  group('MyCar Maintenance Models Tests', () {
    test('MaintenanceItem calculations work correctly', () {
      final item = MaintenanceItem(
        name: 'Test Item',
        description: 'Test Description',
        iconPath: 'test/icon.png',
        defaultIntervalKm: 10000,
        currentKm: 15000,
        lastMaintenanceKm: 10000,
        category: 'Test',
      );

      // Test kilométrage restant
      expect(item.kmUntilNextMaintenance, equals(5000));

      // Test pourcentage d'usure
      expect(item.usagePercentage, equals(0.5));

      // Test si entretien est dû
      expect(item.isMaintenanceDue, isFalse);
      expect(item.isMaintenanceApproaching, isFalse);
    });

    test('MaintenanceItem with overdue maintenance', () {
      final item = MaintenanceItem(
        name: 'Overdue Item',
        description: 'Test Description',
        iconPath: 'test/icon.png',
        defaultIntervalKm: 10000,
        currentKm: 25000,
        lastMaintenanceKm: 10000,
        category: 'Test',
      );

      // Test kilométrage restant (négatif car en retard)
      expect(item.kmUntilNextMaintenance, equals(-5000));

      // Test si entretien est dû
      expect(item.isMaintenanceDue, isTrue);
    });

    test('VehicleConfig creation works correctly', () {
      final vehicle = VehicleConfig(
        vehicleName: 'Test Car',
        brand: 'Test Brand',
        model: 'Test Model',
        year: 2020,
        currentKilometers: 50000,
        lastKmUpdate: DateTime.now(),
      );

      expect(vehicle.vehicleName, equals('Test Car'));
      expect(vehicle.brand, equals('Test Brand'));
      expect(vehicle.currentKilometers, equals(50000));
    });
  });
}
