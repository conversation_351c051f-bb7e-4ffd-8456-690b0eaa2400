import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../models/maintenance_item.dart';
import '../widgets/modern_date_picker.dart';
import '../services/currency_service.dart';

class PerformMaintenanceScreen extends StatefulWidget {
  final MaintenanceItem item;

  const PerformMaintenanceScreen({super.key, required this.item});

  @override
  State<PerformMaintenanceScreen> createState() =>
      _PerformMaintenanceScreenState();
}

class _PerformMaintenanceScreenState extends State<PerformMaintenanceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _kilometersController = TextEditingController();
  final _costController = TextEditingController();
  final _locationController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeKilometers();

    // Bloquer la création depuis le diagnostic pour la catégorie "Autre"
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.item.category.toLowerCase() == 'autre') {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('La création d\'une opération "Autre" n\'est pas autorisée depuis le diagnostic.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
          Navigator.pop(context);
        }
      }
    });
  }

  void _initializeKilometers() {
    // Pré-remplir avec le kilométrage actuel du véhicule pour faciliter la saisie
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<MaintenanceProvider>();
      final currentKm = provider.currentVehicleKilometers;
      if (currentKm > 0) {
        _kilometersController.text = currentKm.toString();
      }
    });
  }

  @override
  void dispose() {
    _kilometersController.dispose();
    _costController.dispose();
    _locationController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Entretien',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Informations sur l'élément d'entretien
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Theme.of(
                                context,
                              ).colorScheme.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              _getIconForCategory(widget.item.category),
                              color: Theme.of(context).colorScheme.primary,
                              size: 28,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.item.name,
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                      ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  widget.item.description,
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                        fontSize: 11,
                                      ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 16),

                      Row(
                        children: [
                          Expanded(
                            child: _buildInfoItem(
                              'Km actuel',
                              '${widget.item.currentKm} km',
                              MdiIcons.speedometer,
                            ),
                          ),
                          Expanded(
                            child: _buildInfoItem(
                              'Dernier',
                              '${widget.item.lastMaintenanceKm} km',
                              MdiIcons.history,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Formulaire d'entretien
              Text(
                'Détails',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 16),

              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Date
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: Icon(
                          MdiIcons.calendar,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        title: const Text(
                          'Date',
                          style: TextStyle(fontSize: 13),
                        ),
                        subtitle: Text(
                          '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                        ),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: _selectDate,
                      ),

                      const Divider(),

                      // Kilométrage
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _kilometersController,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        decoration: InputDecoration(
                          labelText: 'Kilométrage de l\'entretien',
                          hintText: 'km lors de l\'entretien',
                          suffixText: 'km',
                          prefixIcon: Icon(MdiIcons.speedometer),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez saisir le kilométrage';
                          }
                          final km = int.tryParse(value);
                          if (km == null || km <= 0) {
                            return 'Kilométrage invalide';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Coût
                      TextFormField(
                        controller: _costController,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                            RegExp(r'^\d+\.?\d{0,2}'),
                          ),
                        ],
                        decoration: InputDecoration(
                          labelText: 'Coût',
                          hintText: '0.00',
                          suffixText: CurrencyService().getCurrencySuffix(),
                          prefixText: CurrencyService().getCurrencyPrefix(),
                          prefixIcon: Icon(CurrencyService().getCurrencyIcon()),
                          border: const OutlineInputBorder(),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Lieu
                      TextFormField(
                        controller: _locationController,
                        decoration: InputDecoration(
                          labelText: 'Lieu',
                          hintText: 'Garage',
                          prefixIcon: Icon(MdiIcons.mapMarker),
                          border: const OutlineInputBorder(),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Notes
                      TextFormField(
                        controller: _notesController,
                        maxLines: 4,
                        decoration: InputDecoration(
                          labelText: 'Notes',
                          hintText: 'Observations',
                          prefixIcon: Icon(MdiIcons.noteText),
                          border: const OutlineInputBorder(),
                          alignLabelWithHint: true,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Boutons d'action
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _performMaintenance,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.check, size: 18),
                  label: Text(
                    _isLoading ? 'Enregistrement...' : 'Confirmer',
                    style: const TextStyle(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),

              const SizedBox(height: 12),

              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _isLoading ? null : () => Navigator.pop(context),
                  icon: const Icon(Icons.cancel, size: 18),
                  label: const Text('Annuler', style: TextStyle(fontSize: 12)),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Information
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.green[700],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Entretien enregistré dans l\'historique.',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontSize: 11,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontSize: 11,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await ModernDatePicker.show(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      helpText: 'Date d\'entretien',
      cancelText: 'Annuler',
      confirmText: 'Valider',
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _performMaintenance() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final cost = _costController.text.isEmpty
          ? null
          : double.tryParse(_costController.text);

      final location = _locationController.text.trim().isEmpty
          ? null
          : _locationController.text.trim();

      final notes = _notesController.text.trim().isEmpty
          ? null
          : _notesController.text.trim();

      final kilometers = int.parse(_kilometersController.text);
      final provider = context.read<MaintenanceProvider>();

      // Effectuer l'entretien avec le kilométrage spécifique et la date choisie
      await provider.performMaintenance(
        widget.item,
        kilometersAtMaintenance: kilometers,
        notes: notes,
        cost: cost,
        location: location,
        maintenanceDate: _selectedDate,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Entretien enregistré'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // Retourner true pour indiquer le succès
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  IconData _getIconForCategory(String category) {
    switch (category.toLowerCase()) {
      case 'moteur':
        return MdiIcons.engine;
      case 'filtres':
        return MdiIcons.airFilter;
      case 'freinage':
        return MdiIcons.carBrakeAlert;
      case 'pneumatiques':
        return MdiIcons.carTireAlert;
      case 'refroidissement':
        return MdiIcons.radiator;
      case 'confort':
        return MdiIcons.airConditioner;
      default:
        return MdiIcons.wrench;
    }
  }
}
