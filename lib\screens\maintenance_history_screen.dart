import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../models/maintenance_history.dart';
import '../widgets/modern_date_picker.dart';
import 'edit_maintenance_kilometers_screen.dart';
import '../models/maintenance_item.dart';
import '../services/currency_service.dart';

class MaintenanceHistoryScreen extends StatefulWidget {
  const MaintenanceHistoryScreen({super.key});

  @override
  State<MaintenanceHistoryScreen> createState() =>
      _MaintenanceHistoryScreenState();
}

class _MaintenanceHistoryScreenState extends State<MaintenanceHistoryScreen> {
  String _searchQuery = '';
  String _selectedCategory = 'Tous';
  DateTimeRange? _selectedDateRange;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Historique',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(MdiIcons.filterVariant, size: 20),
            onPressed: () => _showFilterDialog(context),
            tooltip: 'Filtrer',
          ),
        ],
      ),
      body: Consumer<MaintenanceProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              // Barre de recherche
              _buildSearchBar(),

              // Filtres actifs
              if (_selectedCategory != 'Tous' || _selectedDateRange != null)
                _buildActiveFilters(),

              // Liste de l'historique
              Expanded(
                child: FutureBuilder<List<MaintenanceHistory>>(
                  future: _getFilteredHistory(provider),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (!snapshot.hasData || snapshot.data!.isEmpty) {
                      return _buildEmptyState();
                    }

                    final history = snapshot.data!;
                    return _buildHistoryList(history, provider);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Rechercher dans l\'historique...',
          prefixIcon: Icon(MdiIcons.magnify),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(MdiIcons.close),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildActiveFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      height: 50,
      child: Row(
        children: [
          if (_selectedCategory != 'Tous')
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Chip(
                label: Text(_selectedCategory),
                deleteIcon: Icon(MdiIcons.close, size: 16),
                onDeleted: () {
                  setState(() {
                    _selectedCategory = 'Tous';
                  });
                },
              ),
            ),
          if (_selectedDateRange != null)
            Chip(
              label: Text(
                '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}',
              ),
              deleteIcon: Icon(MdiIcons.close, size: 16),
              onDeleted: () {
                setState(() {
                  _selectedDateRange = null;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildHistoryList(
    List<MaintenanceHistory> history,
    MaintenanceProvider provider,
  ) {
    // Grouper par mois
    final groupedHistory = _groupHistoryByMonth(history);

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedHistory.length,
      itemBuilder: (context, index) {
        final monthYear = groupedHistory.keys.elementAt(index);
        final monthHistory = groupedHistory[monthYear]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête du mois
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Text(
                monthYear,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 14,
                ),
              ),
            ),

            // Entretiens du mois
            ...monthHistory.map(
              (historyItem) => _buildHistoryCard(historyItem, provider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildHistoryCard(
    MaintenanceHistory history,
    MaintenanceProvider provider,
  ) {
    final maintenanceItem = provider.maintenanceItems
        .where((item) => item.id == history.maintenanceItemId)
        .firstOrNull;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec icône et nom
            Row(
              children: [
                Icon(
                  _getIconForMaintenanceItem(maintenanceItem?.name ?? ''),
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        maintenanceItem?.name ?? 'Entretien supprimé',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        _formatDate(history.maintenanceDate),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green[100],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green[300]!),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        MdiIcons.checkCircle,
                        color: Colors.green[700],
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Effectué',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                // Bouton de modification
                IconButton(
                  onPressed: () =>
                      _editMaintenanceKilometers(context, history, provider),
                  icon: Icon(
                    MdiIcons.pencil,
                    color: Colors.blue[600],
                    size: 20,
                  ),
                  tooltip: 'Modifier le kilométrage',
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                  padding: EdgeInsets.zero,
                ),
                const SizedBox(width: 4),
                // Bouton de suppression
                IconButton(
                  onPressed: () =>
                      _showDeleteConfirmationDialog(context, history, provider),
                  icon: Icon(MdiIcons.close, color: Colors.red[600], size: 20),
                  tooltip: 'Supprimer cet entretien',
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                  padding: EdgeInsets.zero,
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Informations détaillées
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'Kilométrage',
                    '${history.kilometersAtMaintenance} km',
                    MdiIcons.speedometer,
                    Colors.blue,
                  ),
                ),
                if (history.cost != null)
                  Expanded(
                    child: _buildInfoItem(
                      'Coût',
                      CurrencyService().formatCostPrecise(history.cost!),
                      CurrencyService().getCurrencyIcon(),
                      Colors.green,
                    ),
                  ),
              ],
            ),

            if (history.location != null || history.mechanicName != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  if (history.location != null)
                    Expanded(
                      child: _buildInfoItem(
                        'Lieu',
                        history.location!,
                        MdiIcons.mapMarker,
                        Colors.orange,
                      ),
                    ),
                  if (history.mechanicName != null)
                    Expanded(
                      child: _buildInfoItem(
                        'Mécanicien',
                        history.mechanicName!,
                        MdiIcons.account,
                        Colors.purple,
                      ),
                    ),
                ],
              ),
            ],

            if (history.notes != null && history.notes!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          MdiIcons.noteText,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Notes',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[700],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Text(
                      history.notes!,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 11, color: Colors.grey[600]),
            ),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 13,
                color: color,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(MdiIcons.history, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Aucun historique trouvé',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Les entretiens effectués apparaîtront ici',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  // Méthodes utilitaires
  Future<List<MaintenanceHistory>> _getFilteredHistory(
    MaintenanceProvider provider,
  ) async {
    // Pour l'instant, retournons une liste vide car nous n'avons pas encore implémenté
    // le système d'historique complet dans la base de données
    return [];
  }

  Map<String, List<MaintenanceHistory>> _groupHistoryByMonth(
    List<MaintenanceHistory> history,
  ) {
    final Map<String, List<MaintenanceHistory>> grouped = {};

    for (final item in history) {
      final monthYear = _getMonthYear(item.maintenanceDate);
      if (!grouped.containsKey(monthYear)) {
        grouped[monthYear] = [];
      }
      grouped[monthYear]!.add(item);
    }

    return grouped;
  }

  String _getMonthYear(DateTime date) {
    const months = [
      'Janvier',
      'Février',
      'Mars',
      'Avril',
      'Mai',
      'Juin',
      'Juillet',
      'Août',
      'Septembre',
      'Octobre',
      'Novembre',
      'Décembre',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  IconData _getIconForMaintenanceItem(String maintenanceName) {
    switch (maintenanceName.toLowerCase()) {
      case 'vidange huile moteur':
        return MdiIcons.oilLevel;
      case 'chaîne de distribution':
        return MdiIcons.linkVariant;
      case 'filtre à air':
        return MdiIcons.airFilter;
      case 'filtre à gasoil':
        return MdiIcons.gasStation;
      case 'filtre habitacle':
        return MdiIcons.airConditioner;
      case 'plaquettes de frein':
        return MdiIcons.carBrakeAlert;
      case 'liquide de frein':
        return MdiIcons.carBrakeRetarder;
      case 'pneus':
        return MdiIcons.carTireAlert;
      case 'liquide de refroidissement':
        return MdiIcons.radiator;
      case 'courroie d\'accessoires':
        return MdiIcons.carCruiseControl;
      default:
        return MdiIcons.wrench;
    }
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                MdiIcons.filterVariant,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Text('Filtres'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Filtre par catégorie
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  labelText: 'Catégorie',
                  border: OutlineInputBorder(),
                ),
                items:
                    [
                          'Tous',
                          'Moteur',
                          'Freinage',
                          'Filtres',
                          'Pneumatiques',
                          'Autres',
                        ]
                        .map(
                          (category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          ),
                        )
                        .toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value ?? 'Tous';
                  });
                },
              ),

              const SizedBox(height: 16),

              // Filtre par date
              OutlinedButton.icon(
                onPressed: () async {
                  final dateRange = await ModernDatePicker.showRange(
                    context: context,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now(),
                    initialDateRange: _selectedDateRange,
                    helpText: 'Période d\'entretien',
                    cancelText: 'Annuler',
                    confirmText: 'Valider',
                  );
                  if (dateRange != null) {
                    setState(() {
                      _selectedDateRange = dateRange;
                    });
                  }
                },
                icon: Icon(MdiIcons.calendar, size: 18),
                label: Text(
                  _selectedDateRange == null
                      ? 'Sélectionner une période'
                      : 'Période sélectionnée',
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedCategory = 'Tous';
                  _selectedDateRange = null;
                  _searchQuery = '';
                  _searchController.clear();
                });
                Navigator.of(context).pop();
              },
              child: const Text('Réinitialiser'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Appliquer'),
            ),
          ],
        );
      },
    );
  }

  // Modifier le kilométrage d'un entretien
  void _editMaintenanceKilometers(
    BuildContext context,
    MaintenanceHistory history,
    MaintenanceProvider provider,
  ) async {
    final maintenanceItem = provider.maintenanceItems
        .where((item) => item.id == history.maintenanceItemId)
        .firstOrNull;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditMaintenanceKilometersScreen(
          history: history,
          maintenanceItem: maintenanceItem,
        ),
      ),
    );

    // Si une modification a été effectuée, recharger les données
    if (result == true) {
      setState(() {
        // Déclencher un rebuild pour actualiser l'affichage
      });
    }
  }

  // Dialogue de confirmation de suppression
  void _showDeleteConfirmationDialog(
    BuildContext context,
    MaintenanceHistory history,
    MaintenanceProvider provider,
  ) {
    final maintenanceItem = provider.maintenanceItems
        .where((item) => item.id == history.maintenanceItemId)
        .firstOrNull;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(MdiIcons.deleteAlert, color: Colors.red, size: 18),
              ),
              const SizedBox(width: 10),
              const Text(
                'Supprimer l\'entretien',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Êtes-vous sûr de vouloir supprimer cet entretien ?',
                style: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // Détails de l'entretien à supprimer
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getIconForMaintenanceItem(
                            maintenanceItem?.name ?? '',
                          ),
                          size: 20,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            maintenanceItem?.name ?? 'Entretien supprimé',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text('Date : ${_formatDate(history.maintenanceDate)}'),
                    Text('Kilométrage : ${history.kilometersAtMaintenance} km'),
                    if (history.cost != null)
                      Text('Coût : ${history.cost!.toStringAsFixed(2)} €'),
                    if (history.location != null)
                      Text('Lieu : ${history.location}'),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Avertissement
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      MdiIcons.alertCircle,
                      color: Colors.red[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Cette action est irréversible. L\'entretien sera définitivement supprimé de l\'historique.',
                        style: TextStyle(color: Colors.red[700], fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: const Text(
                      'Annuler',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () =>
                        _deleteMaintenanceHistory(context, history, provider),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(MdiIcons.delete, size: 14),
                        const SizedBox(width: 4),
                        const Text('Supprimer', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  // Supprimer un entretien de l'historique
  Future<void> _deleteMaintenanceHistory(
    BuildContext context,
    MaintenanceHistory history,
    MaintenanceProvider provider,
  ) async {
    Navigator.of(context).pop(); // Fermer le dialogue

    try {
      await provider.deleteMaintenanceHistory(history.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(MdiIcons.checkCircle, color: Colors.white),
                const SizedBox(width: 8),
                const Text('Entretien supprimé de l\'historique'),
              ],
            ),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Annuler',
              textColor: Colors.white,
              onPressed: () {
                // Note: Pour implémenter l'annulation, il faudrait sauvegarder
                // les données avant suppression et les restaurer
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Fonction d\'annulation à implémenter'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(MdiIcons.alertCircle, color: Colors.white),
                const SizedBox(width: 8),
                Text('Erreur lors de la suppression: $e'),
              ],
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
