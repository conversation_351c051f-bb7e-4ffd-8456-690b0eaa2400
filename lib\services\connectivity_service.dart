import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Service pour gérer la connectivité internet
class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  bool _isConnected = true;
  bool _isInitialized = false;
  
  // Stream controller pour notifier les changements de connectivité
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  
  /// Stream pour écouter les changements de connectivité
  Stream<bool> get connectivityStream => _connectivityController.stream;
  
  /// État actuel de la connectivité
  bool get isConnected => _isConnected;
  bool get isInitialized => _isInitialized;

  /// Initialise le service de connectivité
  Future<void> initialize() async {
    try {
      debugPrint('🌐 Initialisation service connectivité...');
      
      // Vérifier la connectivité initiale
      await _checkInitialConnectivity();
      
      // Écouter les changements de connectivité
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _onConnectivityChanged,
        onError: (error) {
          debugPrint('❌ Erreur connectivité: $error');
        },
      );
      
      _isInitialized = true;
      debugPrint('✅ Service connectivité initialisé - État: ${_isConnected ? "Connecté" : "Déconnecté"}');
      
    } catch (e) {
      debugPrint('❌ Erreur initialisation connectivité: $e');
      _isInitialized = false;
    }
  }

  /// Vérifie la connectivité initiale
  Future<void> _checkInitialConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      await _onConnectivityChanged(connectivityResults);
    } catch (e) {
      debugPrint('❌ Erreur vérification connectivité initiale: $e');
      _isConnected = false;
    }
  }

  /// Gère les changements de connectivité
  Future<void> _onConnectivityChanged(List<ConnectivityResult> results) async {
    try {
      // Vérifier s'il y a une connexion réseau
      final hasConnection = results.any((result) => 
        result == ConnectivityResult.mobile || 
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.ethernet
      );
      
      if (hasConnection) {
        // Il y a une connexion réseau, mais vérifions si internet est accessible
        final hasInternet = await _checkInternetAccess();
        _updateConnectionStatus(hasInternet);
      } else {
        // Pas de connexion réseau
        _updateConnectionStatus(false);
      }
      
    } catch (e) {
      debugPrint('❌ Erreur traitement changement connectivité: $e');
      _updateConnectionStatus(false);
    }
  }

  /// Vérifie l'accès réel à internet
  Future<bool> _checkInternetAccess() async {
    try {
      // Essayer de se connecter à Google DNS
      final result = await InternetAddress.lookup('google.com');
      final isConnected = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      
      debugPrint('🌐 Test connexion internet: ${isConnected ? "Succès" : "Échec"}');
      return isConnected;
      
    } catch (e) {
      debugPrint('🌐 Test connexion internet: Échec ($e)');
      return false;
    }
  }

  /// Met à jour le statut de connexion et notifie les listeners
  void _updateConnectionStatus(bool isConnected) {
    if (_isConnected != isConnected) {
      _isConnected = isConnected;
      
      debugPrint('🌐 Changement connectivité: ${isConnected ? "Connecté ✅" : "Déconnecté ❌"}');
      
      // Notifier les listeners
      _connectivityController.add(isConnected);
    }
  }

  /// Force une vérification de la connectivité
  Future<bool> checkConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      await _onConnectivityChanged(connectivityResults);
      return _isConnected;
    } catch (e) {
      debugPrint('❌ Erreur vérification manuelle connectivité: $e');
      return false;
    }
  }

  /// Teste la connectivité avec un timeout
  Future<bool> testConnectivityWithTimeout({Duration timeout = const Duration(seconds: 5)}) async {
    try {
      final result = await InternetAddress.lookup('google.com').timeout(timeout);
      final isConnected = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      
      _updateConnectionStatus(isConnected);
      return isConnected;
      
    } catch (e) {
      debugPrint('🌐 Test connectivité avec timeout: Échec ($e)');
      _updateConnectionStatus(false);
      return false;
    }
  }

  /// Dispose les ressources
  void dispose() {
    debugPrint('🗑️ Nettoyage service connectivité...');
    _connectivitySubscription?.cancel();
    _connectivityController.close();
  }
}
