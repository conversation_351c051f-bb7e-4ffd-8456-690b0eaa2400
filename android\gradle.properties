org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Disable NDK to avoid compilation issues
android.useDeprecatedNdk=false
android.enableR8.fullMode=false

# Gradle optimizations
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Proxy configuration (disabled)
# systemProp.http.proxyHost=
# systemProp.http.proxyPort=
# systemProp.http.proxyUser=
# systemProp.http.proxyPassword=
# systemProp.http.nonProxyHosts=localhost|127.0.0.1

# HTTPS proxy (disabled)
# systemProp.https.proxyHost=
# systemProp.https.proxyPort=
# systemProp.https.proxyUser=
# systemProp.https.proxyPassword=
# systemProp.https.nonProxyHosts=localhost|127.0.0.1
