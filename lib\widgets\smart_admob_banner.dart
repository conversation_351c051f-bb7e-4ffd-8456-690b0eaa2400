import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../services/admob_service.dart';
import '../services/connectivity_service.dart';

/// Widget intelligent pour afficher une bannière publicitaire AdMob
/// Se masque automatiquement quand il n'y a pas de connexion internet
class SmartAdMobBanner extends StatefulWidget {
  final String pageId;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final bool showLoadingIndicator;

  const SmartAdMobBanner({
    super.key,
    required this.pageId,
    this.margin,
    this.backgroundColor,
    this.showLoadingIndicator = true,
  });

  @override
  State<SmartAdMobBanner> createState() => _SmartAdMobBannerState();
}

class _SmartAdMobBannerState extends State<SmartAdMobBanner> {
  final AdMobService _adMobService = AdMobService();
  final ConnectivityService _connectivityService = ConnectivityService();
  
  StreamSubscription<bool>? _connectivitySubscription;
  bool _isConnected = true;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    // Initialiser la connectivité si nécessaire
    if (!_connectivityService.isInitialized) {
      await _connectivityService.initialize();
    }
    
    // Obtenir l'état initial de connectivité
    _isConnected = _connectivityService.isConnected;
    
    // Écouter les changements de connectivité
    _connectivitySubscription = _connectivityService.connectivityStream.listen((isConnected) {
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
        });
        
        // Si on vient de se reconnecter, recharger la bannière
        if (isConnected && !_adMobService.isBannerAdReady(widget.pageId)) {
          _adMobService.loadBannerAd(widget.pageId);
        }
      }
    });
    
    if (mounted) {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  Widget _buildLoadingContainer(String message) {
    if (!widget.showLoadingIndicator) {
      return const SizedBox.shrink();
    }
    
    return Container(
      height: 50,
      margin: widget.margin ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              message,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🔍 SmartAdMobBanner(${widget.pageId}): build() appelé');
    debugPrint('   - _isInitialized: $_isInitialized');
    debugPrint('   - _isConnected: $_isConnected');
    debugPrint('   - AdMob.isSupported: ${_adMobService.isSupported}');
    debugPrint('   - AdMob.isInitialized: ${_adMobService.isInitialized}');
    debugPrint('   - Banner ready: ${_adMobService.isBannerAdReady(widget.pageId)}');

    // Si pas encore initialisé, afficher un indicateur de chargement
    if (!_isInitialized) {
      debugPrint('❌ SmartAdMobBanner(${widget.pageId}): Pas encore initialisé');
      return widget.showLoadingIndicator
          ? _buildLoadingContainer('Initialisation...')
          : const SizedBox.shrink();
    }

    // 🌐 MASQUER SI PAS DE CONNEXION INTERNET
    if (!_isConnected) {
      debugPrint('❌ SmartAdMobBanner(${widget.pageId}): Pas de connexion - widget masqué');
      return const SizedBox.shrink();
    }

    // Vérifier si AdMob est supporté sur cette plateforme
    if (!_adMobService.isSupported) {
      debugPrint('❌ SmartAdMobBanner(${widget.pageId}): AdMob non supporté');
      return const SizedBox.shrink();
    }

    // Vérifier si AdMob est initialisé
    if (!_adMobService.isInitialized) {
      debugPrint('⏳ SmartAdMobBanner(${widget.pageId}): AdMob non initialisé');
      return widget.showLoadingIndicator
          ? _buildLoadingContainer('Initialisation AdMob...')
          : const SizedBox.shrink();
    }

    // Vérifier si la bannière est prête
    if (!_adMobService.isBannerAdReady(widget.pageId) || _adMobService.getBannerAd(widget.pageId) == null) {
      debugPrint('⏳ SmartAdMobBanner(${widget.pageId}): Bannière non prête');
      return widget.showLoadingIndicator
          ? _buildLoadingContainer('Chargement publicité...')
          : const SizedBox.shrink();
    }

    // Afficher la bannière
    final bannerAd = _adMobService.getBannerAd(widget.pageId)!;
    debugPrint('✅ SmartAdMobBanner(${widget.pageId}): Affichage de la bannière !');
    return Container(
      height: bannerAd.size.height.toDouble(),
      margin: widget.margin ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: AdWidget(ad: bannerAd),
      ),
    );
  }
}

/// Widget bannière spécialement conçu pour le bas de page
class SmartAdMobBottomBanner extends StatelessWidget {
  final String pageId;
  final Color? backgroundColor;

  const SmartAdMobBottomBanner({
    super.key,
    required this.pageId,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SmartAdMobBanner(
          pageId: pageId,
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          backgroundColor: backgroundColor,
          showLoadingIndicator: false, // Pas d'indicateur en bas de page
        ),
      ),
    );
  }
}

/// Widget bannière pour intégration dans les listes
class SmartAdMobListBanner extends StatelessWidget {
  final String pageId;
  final EdgeInsetsGeometry? padding;

  const SmartAdMobListBanner({
    super.key,
    required this.pageId,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 8),
      child: SmartAdMobBanner(
        pageId: pageId,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        backgroundColor: Colors.grey[50],
        showLoadingIndicator: false, // Pas d'indicateur dans les listes
      ),
    );
  }
}
