import 'package:shared_preferences/shared_preferences.dart';

/// Script pour réinitialiser le flag de première installation
/// Utilisez ceci pour tester la fonctionnalité de première installation
void main() async {
  final prefs = await SharedPreferences.getInstance();
  
  // Supprimer le flag de première installation
  await prefs.remove('is_first_launch');
  
  print('✅ Flag de première installation réinitialisé');
  print('🔄 La prochaine ouverture de l\'app sera considérée comme une première installation');
}
