import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/maintenance_item.dart';

class MaintenanceCard extends StatelessWidget {
  final MaintenanceItem item;
  final VoidCallback onTap;
  final bool isUrgent;
  final bool isApproaching;

  const MaintenanceCard({
    super.key,
    required this.item,
    required this.onTap,
    this.isUrgent = false,
    this.isApproaching = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Couleurs modernes basées sur le statut
    Color primaryColor;
    Color backgroundColor;
    Color borderColor;

    if (isUrgent) {
      primaryColor = const Color(0xFFFF6B6B);
      backgroundColor = const Color(0xFFFF6B6B).withValues(alpha: 0.05);
      borderColor = const Color(0xFFFF6B6B).withValues(alpha: 0.2);
    } else if (isApproaching) {
      primaryColor = const Color(0xFFFFB347);
      backgroundColor = const Color(0xFFFFB347).withValues(alpha: 0.05);
      borderColor = const Color(0xFFFFB347).withValues(alpha: 0.2);
    } else {
      primaryColor = const Color(0xFF4ECDC4);
      backgroundColor = Colors.white;
      borderColor = Colors.grey.withValues(alpha: 0.1);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: borderColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // En-tête moderne
                Row(
                  children: [
                    // Icône moderne
                    Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            primaryColor.withValues(alpha: 0.1),
                            primaryColor.withValues(alpha: 0.05),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: primaryColor.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        _getIconForCategory(item.category),
                        color: primaryColor,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Informations principales
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.name,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF2D3748),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            item.description,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: const Color(0xFF718096),
                              fontWeight: FontWeight.w400,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),

                    // Badge de statut moderne
                    _buildModernStatusBadge(context, primaryColor),
                  ],
                ),

                const SizedBox(height: 20),

                // Barre de progression moderne
                _buildModernProgressBar(context, primaryColor),

                const SizedBox(height: 16),

                // Informations de kilométrage modernes
                Row(
                  children: [
                    Expanded(
                      child: _buildModernKmInfo(
                        context,
                        'Dernier',
                        '${item.lastMaintenanceKm} km',
                        MdiIcons.history,
                        primaryColor,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildModernKmInfo(
                        context,
                        item.kmUntilNextMaintenance > 0 ? 'Reste' : 'Dépassé',
                        '${item.kmUntilNextMaintenance.abs()} km',
                        item.kmUntilNextMaintenance > 0
                            ? MdiIcons.clockOutline
                            : MdiIcons.alertCircleOutline,
                        item.kmUntilNextMaintenance > 0
                            ? primaryColor
                            : const Color(0xFFFF6B6B),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernStatusBadge(BuildContext context, Color primaryColor) {
    String text;
    Color color;
    IconData icon;

    if (isUrgent) {
      text = 'URGENT';
      color = const Color(0xFFFF6B6B);
      icon = MdiIcons.alertCircle;
    } else if (isApproaching) {
      text = 'BIENTÔT';
      color = const Color(0xFFFFB347);
      icon = MdiIcons.clockAlert;
    } else {
      text = 'OK';
      color = const Color(0xFF4ECDC4);
      icon = MdiIcons.checkCircle;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernProgressBar(BuildContext context, Color primaryColor) {
    final percentage = (item.usagePercentage * 100).toInt();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progression',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: const Color(0xFF4A5568),
              ),
            ),
            Text(
              '$percentage%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.grey.withValues(alpha: 0.1),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: item.usagePercentage.clamp(0.0, 1.0),
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
              minHeight: 8,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernKmInfo(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 6),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: const Color(0xFF718096),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: const Color(0xFF2D3748),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconForCategory(String category) {
    switch (category.toLowerCase()) {
      case 'moteur':
        return MdiIcons.engine;
      case 'filtres':
        return MdiIcons.airFilter;
      case 'freinage':
        return MdiIcons.carBrakeAlert;
      case 'pneumatiques':
        return MdiIcons.carTireAlert;
      case 'refroidissement':
        return MdiIcons.radiator;
      case 'confort':
        return MdiIcons.airConditioner;
      default:
        return MdiIcons.wrench;
    }
  }
}
