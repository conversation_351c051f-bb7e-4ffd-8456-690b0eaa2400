import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../services/notification_service.dart';
import 'maintenance_detail_screen.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final NotificationService _notificationService = NotificationService();

  @override
  Widget build(BuildContext context) {
    final notifications = _notificationService.notifications;
    final urgentNotifications = _notificationService.urgentNotifications;
    final reminderNotifications = _notificationService.reminderNotifications;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Notifications',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          if (notifications.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.clear_all, size: 18),
              onPressed: _clearAllNotifications,
              tooltip: 'Effacer',
            ),
        ],
      ),
      body: notifications.isEmpty
          ? _buildEmptyState()
          : ListView(
              padding: const EdgeInsets.all(16),
              children: [
                if (urgentNotifications.isNotEmpty) ...[
                  _buildSectionHeader(
                    context,
                    'Urgent',
                    Icons.warning,
                    Colors.red,
                    urgentNotifications.length,
                  ),
                  const SizedBox(height: 12),
                  ...urgentNotifications.map(
                    (notification) =>
                        _buildNotificationCard(context, notification),
                  ),
                  const SizedBox(height: 24),
                ],

                if (reminderNotifications.isNotEmpty) ...[
                  _buildSectionHeader(
                    context,
                    'Rappels',
                    Icons.schedule,
                    Colors.orange,
                    reminderNotifications.length,
                  ),
                  const SizedBox(height: 12),
                  ...reminderNotifications.map(
                    (notification) =>
                        _buildNotificationCard(context, notification),
                  ),
                ],
              ],
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(MdiIcons.bellOff, size: 50, color: Colors.grey[400]),
          const SizedBox(height: 8),
          Text(
            'Aucune notification',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            'Entretiens OK !',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[500],
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    int count,
  ) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 6),
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: color,
            fontSize: 13,
          ),
        ),
        const SizedBox(width: 6),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            count.toString(),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationCard(
    BuildContext context,
    MaintenanceNotification notification,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 6),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: notification.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(notification.icon, color: notification.color, size: 18),
        ),
        title: Text(
          notification.title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: notification.isRead
                ? FontWeight.normal
                : FontWeight.w600,
            fontSize: 12,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              notification.message,
              style: const TextStyle(fontSize: 11),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              _formatDate(notification.createdAt),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontSize: 9,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'mark_read':
                _markAsRead(notification);
                break;
              case 'dismiss':
                _dismissNotification(notification);
                break;
            }
          },
          itemBuilder: (context) => [
            if (!notification.isRead)
              const PopupMenuItem(
                value: 'mark_read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read, size: 14),
                    SizedBox(width: 4),
                    Text('Marquer lu', style: TextStyle(fontSize: 10)),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'dismiss',
              child: Row(
                children: [
                  Icon(Icons.close, size: 14),
                  SizedBox(width: 4),
                  Text('Supprimer', style: TextStyle(fontSize: 10)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _openMaintenanceDetail(context, notification),
        tileColor: notification.isRead
            ? null
            : notification.color.withValues(alpha: 0.05),
      ),
    );
  }

  void _markAsRead(MaintenanceNotification notification) {
    setState(() {
      _notificationService.markAsRead(notification.id);
    });
  }

  void _dismissNotification(MaintenanceNotification notification) {
    setState(() {
      _notificationService.dismissNotification(notification.id);
    });
  }

  void _clearAllNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Effacer notifications',
          style: TextStyle(fontSize: 16),
        ),
        content: const Text(
          'Supprimer toutes les notifications ?',
          style: TextStyle(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                // Clear all notifications by dismissing them one by one
                final notificationIds = _notificationService.notifications
                    .map((n) => n.id)
                    .toList();
                for (final id in notificationIds) {
                  _notificationService.dismissNotification(id);
                }
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Effacer'),
          ),
        ],
      ),
    );
  }

  void _openMaintenanceDetail(
    BuildContext context,
    MaintenanceNotification notification,
  ) {
    // Marquer comme lu
    _markAsRead(notification);

    // Naviguer vers le détail
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            MaintenanceDetailScreen(item: notification.maintenanceItem),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inHours < 1) {
      return 'Il y a ${difference.inMinutes} min';
    } else if (difference.inDays < 1) {
      return 'Il y a ${difference.inHours}h';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays} jours';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
