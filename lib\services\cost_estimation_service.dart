class CostEstimationService {
  static final CostEstimationService _instance = CostEstimationService._internal();
  factory CostEstimationService() => _instance;
  CostEstimationService._internal();

  // Coûts moyens estimés par type d'entretien (en euros)
  static const Map<String, double> _defaultCosts = {
    'Vidange Huile Moteur': 80.0,
    'Courroie de Distribution': 800.0,
    'Filtre à Air': 25.0,
    'Filtre à Gasoil': 35.0,
    'Filtre Habitacle': 30.0,
    'Plaquettes de Frein': 150.0,
    'Liquide de Frein': 45.0,
    'Pneus': 400.0, // Pour un jeu de 4 pneus
    'Liquide de Refroidissement': 60.0,
  };

  // Coûts par catégorie (moyenne)
  static const Map<String, double> _categoryCosts = {
    'Moteur': 400.0,
    'Filtres': 30.0,
    'Freinage': 100.0,
    'Pneumatiques': 400.0,
    'Refroidissement': 60.0,
    'Confort': 30.0,
  };

  /// Obtenir le coût estimé pour un entretien spécifique
  double getEstimatedCost(String maintenanceName) {
    return _defaultCosts[maintenanceName] ?? 50.0; // Coût par défaut si non trouvé
  }

  /// Obtenir le coût moyen par catégorie
  double getCategoryCost(String category) {
    return _categoryCosts[category] ?? 50.0;
  }

  /// Calculer le coût total estimé pour une liste d'entretiens
  double calculateTotalEstimatedCost(List<String> maintenanceNames) {
    double total = 0.0;
    for (final name in maintenanceNames) {
      total += getEstimatedCost(name);
    }
    return total;
  }

  /// Obtenir tous les coûts par défaut
  Map<String, double> getAllDefaultCosts() {
    return Map.from(_defaultCosts);
  }

  /// Obtenir les coûts par catégorie
  Map<String, double> getAllCategoryCosts() {
    return Map.from(_categoryCosts);
  }

  /// Calculer le coût annuel estimé basé sur les intervalles
  double calculateAnnualEstimatedCost(Map<String, int> maintenanceIntervals, int averageKmPerYear) {
    double annualCost = 0.0;
    
    _defaultCosts.forEach((maintenanceName, cost) {
      final interval = maintenanceIntervals[maintenanceName] ?? 20000;
      final frequency = averageKmPerYear / interval;
      annualCost += cost * frequency;
    });
    
    return annualCost;
  }

  /// Calculer le coût par kilomètre
  double calculateCostPerKm(String maintenanceName, int intervalKm) {
    final cost = getEstimatedCost(maintenanceName);
    return cost / intervalKm;
  }

  /// Obtenir les entretiens les plus coûteux
  List<MapEntry<String, double>> getMostExpensiveMaintenance({int limit = 5}) {
    final sortedCosts = _defaultCosts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedCosts.take(limit).toList();
  }

  /// Obtenir les entretiens les moins coûteux
  List<MapEntry<String, double>> getLeastExpensiveMaintenance({int limit = 5}) {
    final sortedCosts = _defaultCosts.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    return sortedCosts.take(limit).toList();
  }

  /// Calculer le budget mensuel recommandé
  double calculateMonthlyBudget(int averageKmPerYear) {
    final annualCost = calculateAnnualEstimatedCost({}, averageKmPerYear);
    return annualCost / 12;
  }

  /// Formater un montant avec la devise actuelle
  String formatCost(double cost) {
    // Note: Cette méthode est maintenant dépréciée
    // Utilisez CurrencyService().formatCostCompact() à la place
    if (cost >= 1000) {
      return '${(cost / 1000).toStringAsFixed(1)}k €';
    } else {
      return '${cost.toStringAsFixed(0)} €';
    }
  }

  /// Obtenir une estimation de coût avec une marge d'erreur
  Map<String, double> getCostRange(String maintenanceName, {double marginPercent = 20.0}) {
    final baseCost = getEstimatedCost(maintenanceName);
    final margin = baseCost * (marginPercent / 100);
    
    return {
      'min': baseCost - margin,
      'max': baseCost + margin,
      'average': baseCost,
    };
  }

  /// Calculer les économies potentielles avec un entretien préventif
  double calculatePreventiveSavings(String maintenanceName) {
    // Les réparations d'urgence coûtent généralement 2-3x plus cher
    final preventiveCost = getEstimatedCost(maintenanceName);
    final emergencyCost = preventiveCost * 2.5;
    
    return emergencyCost - preventiveCost;
  }

  /// Obtenir des conseils de budget basés sur l'usage
  Map<String, dynamic> getBudgetAdvice(int currentKm, int averageKmPerYear) {
    final monthlyBudget = calculateMonthlyBudget(averageKmPerYear);
    final annualBudget = monthlyBudget * 12;
    
    String advice;
    if (averageKmPerYear < 10000) {
      advice = 'Usage faible : Budget d\'entretien réduit recommandé';
    } else if (averageKmPerYear < 20000) {
      advice = 'Usage normal : Budget d\'entretien standard';
    } else {
      advice = 'Usage intensif : Budget d\'entretien majoré recommandé';
    }
    
    return {
      'monthlyBudget': monthlyBudget,
      'annualBudget': annualBudget,
      'advice': advice,
      'kmPerYear': averageKmPerYear,
    };
  }
}
