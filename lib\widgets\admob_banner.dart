import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../services/admob_service.dart';
import '../services/connectivity_service.dart';

/// Widget pour afficher une bannière publicitaire AdMob
class AdMobBanner extends StatefulWidget {
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;

  const AdMobBanner({
    super.key,
    this.margin,
    this.backgroundColor,
  });

  @override
  State<AdMobBanner> createState() => _AdMobBannerState();
}

class _AdMobBannerState extends State<AdMobBanner> {
  final AdMobService _adMobService = AdMobService();
  final ConnectivityService _connectivityService = ConnectivityService();
  late Timer _timer;
  StreamSubscription<bool>? _connectivitySubscription;
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();

    // Initialiser la connectivité si nécessaire
    _initializeConnectivity();

    // Écouter les changements de connectivité
    _connectivitySubscription = _connectivityService.connectivityStream.listen((isConnected) {
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
        });
      }
    });

    // Timer pour mettre à jour l'état toutes les secondes
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  Future<void> _initializeConnectivity() async {
    if (!_connectivityService.isInitialized) {
      await _connectivityService.initialize();
    }
    if (mounted) {
      setState(() {
        _isConnected = _connectivityService.isConnected;
      });
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Vérifier si AdMob est supporté sur cette plateforme
    if (!_adMobService.isSupported) {
      return const SizedBox.shrink();
    }

    // Vérifier si AdMob est initialisé
    if (!_adMobService.isInitialized) {
      return Container(
        height: 50,
        margin: widget.margin ?? const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Initialisation AdMob...',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Vérifier si la bannière est prête
    if (!_adMobService.isBannerAdReady || _adMobService.bannerAd == null) {
      return Container(
        height: 50,
        margin: widget.margin ?? const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Chargement publicité...',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Afficher la bannière
    return Container(
      height: _adMobService.bannerAd!.size.height.toDouble(),
      margin: widget.margin ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: AdWidget(ad: _adMobService.bannerAd!),
      ),
    );
  }
}

/// Widget bannière spécialement conçu pour le bas de page
class AdMobBottomBanner extends StatelessWidget {
  final Color? backgroundColor;

  const AdMobBottomBanner({
    super.key,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: AdMobBanner(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          backgroundColor: backgroundColor,
        ),
      ),
    );
  }
}

/// Widget bannière pour intégration dans les listes
class AdMobListBanner extends StatelessWidget {
  final EdgeInsetsGeometry? padding;

  const AdMobListBanner({
    super.key,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 8),
      child: AdMobBanner(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        backgroundColor: Colors.grey[50],
      ),
    );
  }
}
