import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'providers/maintenance_provider.dart';
import 'services/theme_service.dart';
import 'services/currency_service.dart';
import 'services/admob_service.dart';
import 'services/connectivity_service.dart';
import 'screens/home_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialiser SQLite FFI pour toutes les plateformes non-mobiles
  if (kIsWeb ||
      defaultTargetPlatform == TargetPlatform.windows ||
      defaultTargetPlatform == TargetPlatform.linux ||
      defaultTargetPlatform == TargetPlatform.macOS) {
    // Initialiser FFI
    sqfliteFfiInit();
    // Définir le factory de base de données
    databaseFactory = databaseFactoryFfi;
  }

  // Initialiser AdMob
  await AdMobService().initialize();

  // Initialiser ConnectivityService
  await ConnectivityService().initialize();

  // Initialiser CurrencyService
  await CurrencyService().initialize();

  runApp(const MyCarMaintenanceApp());
}

class MyCarMaintenanceApp extends StatelessWidget {
  const MyCarMaintenanceApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => MaintenanceProvider()),
        ChangeNotifierProvider(create: (context) => ThemeService()),
        ChangeNotifierProvider(create: (context) => CurrencyService()),
      ],
      child: Consumer<ThemeService>(
        builder: (context, themeService, child) {
          // Configurer la barre de statut en bleu
          SystemChrome.setSystemUIOverlayStyle(
            const SystemUiOverlayStyle(
              statusBarColor: Color(0xFF1E40AF), // Bleu pour la barre de statut
              statusBarIconBrightness: Brightness.light, // Icônes blanches
              statusBarBrightness: Brightness.dark, // Pour iOS
            ),
          );

          return MaterialApp(
            title: 'Carosti',
            debugShowCheckedModeBanner: false,
            theme: themeService.getLightTheme(),
            themeMode: ThemeMode.light, // Toujours mode clair
            // Configuration de la localisation française
            locale: const Locale('fr', 'FR'),
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('fr', 'FR'), // Français
              Locale('en', 'US'), // Anglais (fallback)
            ],
            home: const AppInitializer(),
          );
        },
      ),
    );
  }
}

class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    if (!mounted) return;

    // Initialiser le service de thème
    final themeService = context.read<ThemeService>();
    await themeService.initialize();

    if (!mounted) return;

    // Initialiser le provider de maintenance
    final maintenanceProvider = context.read<MaintenanceProvider>();
    await maintenanceProvider.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return const HomeScreen();
  }
}
