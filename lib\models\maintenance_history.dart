class MaintenanceHistory {
  final int? id;
  final int maintenanceItemId;
  final int kilometersAtMaintenance;
  final DateTime maintenanceDate;
  final String? notes;
  final double? cost;
  final String? location;
  final String? mechanicName;
  final List<String>? photos;
  final bool isCompleted;

  MaintenanceHistory({
    this.id,
    required this.maintenanceItemId,
    required this.kilometersAtMaintenance,
    required this.maintenanceDate,
    this.notes,
    this.cost,
    this.location,
    this.mechanicName,
    this.photos,
    this.isCompleted = true,
  });

  // Convertir vers Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'maintenanceItemId': maintenanceItemId,
      'kilometersAtMaintenance': kilometersAtMaintenance,
      'maintenanceDate': maintenanceDate.millisecondsSinceEpoch,
      'notes': notes,
      'cost': cost,
      'location': location,
      'mechanicName': mechanicName,
      'photos': photos?.join(','), // Stocker les chemins des photos séparés par des virgules
      'isCompleted': isCompleted ? 1 : 0,
    };
  }

  // Créer depuis Map (base de données)
  factory MaintenanceHistory.fromMap(Map<String, dynamic> map) {
    return MaintenanceHistory(
      id: map['id'],
      maintenanceItemId: map['maintenanceItemId'],
      kilometersAtMaintenance: map['kilometersAtMaintenance'],
      maintenanceDate: DateTime.fromMillisecondsSinceEpoch(map['maintenanceDate']),
      notes: map['notes'],
      cost: map['cost']?.toDouble(),
      location: map['location'],
      mechanicName: map['mechanicName'],
      photos: map['photos'] != null 
          ? (map['photos'] as String).split(',').where((s) => s.isNotEmpty).toList()
          : null,
      isCompleted: map['isCompleted'] == 1,
    );
  }

  // Copier avec modifications
  MaintenanceHistory copyWith({
    int? id,
    int? maintenanceItemId,
    int? kilometersAtMaintenance,
    DateTime? maintenanceDate,
    String? notes,
    double? cost,
    String? location,
    String? mechanicName,
    List<String>? photos,
    bool? isCompleted,
  }) {
    return MaintenanceHistory(
      id: id ?? this.id,
      maintenanceItemId: maintenanceItemId ?? this.maintenanceItemId,
      kilometersAtMaintenance: kilometersAtMaintenance ?? this.kilometersAtMaintenance,
      maintenanceDate: maintenanceDate ?? this.maintenanceDate,
      notes: notes ?? this.notes,
      cost: cost ?? this.cost,
      location: location ?? this.location,
      mechanicName: mechanicName ?? this.mechanicName,
      photos: photos ?? this.photos,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  @override
  String toString() {
    return 'MaintenanceHistory{id: $id, itemId: $maintenanceItemId, km: $kilometersAtMaintenance, date: $maintenanceDate}';
  }
}
