class DocumentItem {
  final int? id;
  final String name;
  final String description;
  final String iconPath;
  final DateTime? expirationDate;
  final String? companyName;
  final String? documentNumber;
  final String? notes;
  final String category;
  final bool isActive;
  final int? vehicleId;

  DocumentItem({
    this.id,
    required this.name,
    required this.description,
    required this.iconPath,
    this.expirationDate,
    this.companyName,
    this.documentNumber,
    this.notes,
    required this.category,
    this.isActive = true,
    this.vehicleId,
  });

  // Calculer les jours restants avant expiration
  int get daysUntilExpiration {
    if (expirationDate == null) return 999;
    final now = DateTime.now();
    final difference = expirationDate!.difference(now).inDays;
    return difference;
  }

  // Vérifier si le document est expiré
  bool get isExpired => daysUntilExpiration < 0;

  // Vérifier si le document expire bientôt
  bool get isExpiringSoon => daysUntilExpiration <= 30 && daysUntilExpiration >= 0;

  // Vérifier si le document est urgent (< 7 jours)
  bool get isUrgent => daysUntilExpiration <= 7 && daysUntilExpiration >= 0;

  // Obtenir le statut du document
  String get status {
    if (isExpired) return 'EXPIRÉ';
    if (isUrgent) return 'URGENT';
    if (isExpiringSoon) return 'BIENTÔT';
    return 'VALIDE';
  }

  // Obtenir la couleur selon l'état
  String get statusColor {
    if (isExpired || isUrgent) return 'red';
    if (daysUntilExpiration <= 15) return 'orange';
    if (isExpiringSoon) return 'blue';
    return 'green';
  }

  // Convertir vers Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconPath': iconPath,
      'expirationDate': expirationDate?.millisecondsSinceEpoch,
      'companyName': companyName,
      'documentNumber': documentNumber,
      'notes': notes,
      'category': category,
      'isActive': isActive ? 1 : 0,
      'vehicle_id': vehicleId,
    };
  }

  // Créer depuis Map (base de données)
  factory DocumentItem.fromMap(Map<String, dynamic> map) {
    return DocumentItem(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      iconPath: map['iconPath'],
      expirationDate: map['expirationDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['expirationDate'])
          : null,
      companyName: map['companyName'],
      documentNumber: map['documentNumber'],
      notes: map['notes'],
      category: map['category'],
      isActive: map['isActive'] == 1,
      vehicleId: map['vehicle_id'],
    );
  }

  // Copier avec modifications
  DocumentItem copyWith({
    int? id,
    String? name,
    String? description,
    String? iconPath,
    DateTime? expirationDate,
    String? companyName,
    String? documentNumber,
    String? notes,
    String? category,
    bool? isActive,
    int? vehicleId,
  }) {
    return DocumentItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconPath: iconPath ?? this.iconPath,
      expirationDate: expirationDate ?? this.expirationDate,
      companyName: companyName ?? this.companyName,
      documentNumber: documentNumber ?? this.documentNumber,
      notes: notes ?? this.notes,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      vehicleId: vehicleId ?? this.vehicleId,
    );
  }

  // Marquer comme renouvelé
  DocumentItem markAsRenewed({
    required DateTime newExpirationDate,
    String? newCompanyName,
    String? newDocumentNumber,
    String? newNotes,
  }) {
    return copyWith(
      expirationDate: newExpirationDate,
      companyName: newCompanyName ?? companyName,
      documentNumber: newDocumentNumber ?? documentNumber,
      notes: newNotes ?? notes,
    );
  }
}
