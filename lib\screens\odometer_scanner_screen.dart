import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class OdometerScannerScreen extends StatefulWidget {
  const OdometerScannerScreen({super.key});

  @override
  State<OdometerScannerScreen> createState() => _OdometerScannerScreenState();
}

class _OdometerScannerScreenState extends State<OdometerScannerScreen>
    with TickerProviderStateMixin {
  CameraController? _controller;
  List<CameraDescription>? _cameras;
  bool _isInitialized = false;
  bool _isFlashOn = false;
  bool _isCapturing = false;

  late AnimationController _animationController;
  late Animation<double> _animation;

  final TextRecognizer _textRecognizer = TextRecognizer();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      _cameras = await availableCameras();
      if (_cameras!.isNotEmpty) {
        _controller = CameraController(
          _cameras![0],
          ResolutionPreset.high,
          enableAudio: false,
        );
        await _controller!.initialize();
        if (mounted) {
          setState(() {
            _isInitialized = true;
          });
        }
      }
    } catch (e) {
      debugPrint('Erreur initialisation caméra: $e');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _controller?.dispose();
    _textRecognizer.close();
    super.dispose();
  }

  Future<void> _toggleFlash() async {
    if (_controller != null) {
      try {
        await _controller!.setFlashMode(
          _isFlashOn ? FlashMode.off : FlashMode.torch,
        );
        setState(() {
          _isFlashOn = !_isFlashOn;
        });
      } catch (e) {
        debugPrint('Erreur flash: $e');
      }
    }
  }

  Future<void> _captureAndAnalyze() async {
    if (_controller != null &&
        _controller!.value.isInitialized &&
        !_isCapturing) {
      setState(() {
        _isCapturing = true;
      });

      try {
        final image = await _controller!.takePicture();
        debugPrint('Image capturée: ${image.path}');

        // Analyse OCR réelle
        final inputImage = InputImage.fromFilePath(image.path);
        final recognizedText = await _textRecognizer.processImage(inputImage);

        // Extraction des chiffres du kilométrage
        String? detectedKm = _extractKilometers(recognizedText.text);

        if (detectedKm != null && detectedKm.isNotEmpty) {
          if (mounted) {
            Navigator.of(context).pop(detectedKm);
          }
        } else {
          // Si aucun kilométrage détecté, proposer une saisie manuelle
          if (mounted) {
            final result = await _showManualInputDialog();
            if (result != null && mounted) {
              Navigator.of(context).pop(result);
            }
          }
        }
      } catch (e) {
        debugPrint('Erreur capture/OCR: $e');
        if (mounted) {
          // En cas d'erreur, proposer une saisie manuelle
          final result = await _showManualInputDialog();
          if (result != null && mounted) {
            Navigator.of(context).pop(result);
          }
        }
      } finally {
        if (mounted) {
          setState(() {
            _isCapturing = false;
          });
        }
      }
    }
  }

  String? _extractKilometers(String text) {
    debugPrint('Texte OCR détecté: $text');

    // Nettoyer le texte d'abord
    String cleanText = text
        .replaceAll(
          RegExp(r'[^\d\s,.]'),
          ' ',
        ) // Garder seulement chiffres, espaces, virgules, points
        .replaceAll(RegExp(r'\s+'), ' ') // Normaliser les espaces
        .trim();

    debugPrint('Texte nettoyé: $cleanText');

    // Recherche de patterns de kilométrage plus précis
    final patterns = [
      // Patterns avec séparateurs (123 456, 123,456, 123.456)
      RegExp(r'\b(\d{1,3}[,.\s]\d{3,6})\b'),
      // Patterns sans séparateurs (123456)
      RegExp(r'\b(\d{4,7})\b'),
      // Patterns avec moins de chiffres mais plausibles
      RegExp(r'\b(\d{3,6})\b'),
    ];

    List<String> candidates = [];

    for (final pattern in patterns) {
      final matches = pattern.allMatches(cleanText);
      for (final match in matches) {
        final kmText = match.group(1);
        if (kmText != null) {
          // Nettoyer le texte (supprimer espaces, virgules, points)
          final cleanKm = kmText.replaceAll(RegExp(r'[,.\s]'), '');
          final km = int.tryParse(cleanKm);

          // Vérifier que c'est un kilométrage plausible
          if (km != null && km >= 100 && km <= 999999) {
            candidates.add(km.toString());
            debugPrint('Candidat kilométrage trouvé: $km');
          }
        }
      }
    }

    // Retourner le candidat le plus plausible (le plus long)
    if (candidates.isNotEmpty) {
      candidates.sort((a, b) => b.length.compareTo(a.length));
      return candidates.first;
    }

    return null;
  }

  Future<String?> _showManualInputDialog() async {
    final controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Kilométrage non détecté',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Veuillez saisir le kilométrage manuellement :',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              autofocus: true,
              decoration: const InputDecoration(
                labelText: 'Kilométrage',
                suffixText: 'km',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler', style: TextStyle(fontSize: 12)),
          ),
          ElevatedButton(
            onPressed: () {
              final km = controller.text.trim();
              if (km.isNotEmpty && int.tryParse(km) != null) {
                Navigator.of(context).pop(km);
              }
            },
            child: const Text('Valider', style: TextStyle(fontSize: 12)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: const Text(
          'Scanner Tableau',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isFlashOn ? MdiIcons.flashlight : MdiIcons.flashlightOff,
              size: 20,
            ),
            onPressed: _toggleFlash,
            tooltip: 'Flash',
          ),
        ],
      ),
      body: _isInitialized
          ? Stack(
              children: [
                // Aperçu caméra
                Positioned.fill(child: CameraPreview(_controller!)),

                // Overlay avec rectangle de centrage
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                    ),
                    child: Stack(
                      children: [
                        // Zone de scan avec animation
                        Center(
                          child: AnimatedBuilder(
                            animation: _animation,
                            builder: (context, child) {
                              return Container(
                                width: 280,
                                height: 120,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.white.withValues(
                                      alpha: 0.7 + (_animation.value * 0.3),
                                    ),
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Stack(
                                  children: [
                                    // Coins du rectangle
                                    Positioned(
                                      top: 8,
                                      left: 8,
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            top: BorderSide(
                                              color: Colors.green,
                                              width: 3,
                                            ),
                                            left: BorderSide(
                                              color: Colors.green,
                                              width: 3,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      top: 8,
                                      right: 8,
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            top: BorderSide(
                                              color: Colors.green,
                                              width: 3,
                                            ),
                                            right: BorderSide(
                                              color: Colors.green,
                                              width: 3,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      bottom: 8,
                                      left: 8,
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            bottom: BorderSide(
                                              color: Colors.green,
                                              width: 3,
                                            ),
                                            left: BorderSide(
                                              color: Colors.green,
                                              width: 3,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      bottom: 8,
                                      right: 8,
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            bottom: BorderSide(
                                              color: Colors.green,
                                              width: 3,
                                            ),
                                            right: BorderSide(
                                              color: Colors.green,
                                              width: 3,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    // Ligne de scan animée
                                    Positioned(
                                      top: 10 + (_animation.value * 100),
                                      left: 10,
                                      right: 10,
                                      child: Container(
                                        height: 2,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              Colors.transparent,
                                              Colors.green,
                                              Colors.transparent,
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),

                        // Instructions améliorées
                        Positioned(
                          top: 80,
                          left: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            margin: const EdgeInsets.symmetric(horizontal: 20),
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.8),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      MdiIcons.speedometer,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    const Text(
                                      'Centrez le kilométrage',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                const Text(
                                  'Alignez les chiffres dans le rectangle',
                                  style: TextStyle(
                                    color: Colors.white70,
                                    fontSize: 12,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Conseils de positionnement
                        Positioned(
                          bottom: 140,
                          left: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            margin: const EdgeInsets.symmetric(horizontal: 30),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.9),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  MdiIcons.lightbulbOnOutline,
                                  color: Colors.white,
                                  size: 14,
                                ),
                                const SizedBox(width: 6),
                                const Text(
                                  'Assurez-vous que les chiffres sont nets',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Bouton de capture
                Positioned(
                  bottom: 40,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: GestureDetector(
                      onTap: _isCapturing ? null : _captureAndAnalyze,
                      child: Container(
                        width: 70,
                        height: 70,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _isCapturing ? Colors.grey[300] : Colors.white,
                          border: Border.all(
                            color: _isCapturing
                                ? Colors.grey[400]!
                                : Colors.grey[300]!,
                            width: 4,
                          ),
                        ),
                        child: _isCapturing
                            ? const CircularProgressIndicator(
                                strokeWidth: 3,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.black54,
                                ),
                              )
                            : Icon(
                                MdiIcons.camera,
                                color: Colors.black,
                                size: 30,
                              ),
                      ),
                    ),
                  ),
                ),
              ],
            )
          : const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Colors.white),
                  SizedBox(height: 16),
                  Text(
                    'Initialisation caméra...',
                    style: TextStyle(color: Colors.white, fontSize: 14),
                  ),
                ],
              ),
            ),
    );
  }
}
